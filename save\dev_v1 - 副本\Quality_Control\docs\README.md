# 质控系统详细文档

本目录包含质控系统的详细技术文档，涵盖系统重构、代码清理、修改历史等各个方面。

## 📚 文档索引

### 核心文档

| 文档 | 描述 | 适用对象 |
|------|------|----------|
| [`README_SYSTEM.md`](README_SYSTEM.md) | 系统架构详细说明 | 开发者、架构师 |
| [`README_REFACTOR.md`](README_REFACTOR.md) | 系统重构详细说明 | 开发者、维护者 |
| [`README_CODE_CLEANING.md`](README_CODE_CLEANING.md) | 代码清理解决方案 | 开发者、运维人员 |
| [`README_MODIFICATIONS.md`](README_MODIFICATIONS.md) | 系统修改历史 | 项目管理者、开发者 |

## 📖 文档概览

### 系统架构文档 (`README_SYSTEM.md`)
- **内容**：详细的系统架构说明
- **包含**：控制层架构、核心组件、数据流程
- **适用场景**：了解系统整体设计和组件关系

### 重构说明文档 (`README_REFACTOR.md`)
- **内容**：v2.0重构的完整说明
- **包含**：三层架构设计、文件组织、命名规范
- **适用场景**：理解系统重构的背景和实现

### 代码清理文档 (`README_CODE_CLEANING.md`)
- **内容**：代码格式问题的解决方案
- **包含**：问题分析、清理工具、使用指南
- **适用场景**：解决生成代码的格式问题

### 修改历史文档 (`README_MODIFICATIONS.md`)
- **内容**：v2.1版本的具体修改
- **包含**：测试文件重组、目录命名规范化
- **适用场景**：了解最新的系统修改和改进

## 🎯 快速导航

### 按角色分类

#### 🔧 开发者
- 系统架构：[`README_SYSTEM.md`](README_SYSTEM.md)
- 重构说明：[`README_REFACTOR.md`](README_REFACTOR.md)
- 代码清理：[`README_CODE_CLEANING.md`](README_CODE_CLEANING.md)

#### 👨‍💼 项目管理者
- 修改历史：[`README_MODIFICATIONS.md`](README_MODIFICATIONS.md)
- 系统概览：[`README_SYSTEM.md`](README_SYSTEM.md)

#### 🛠️ 运维人员
- 代码清理：[`README_CODE_CLEANING.md`](README_CODE_CLEANING.md)
- 故障排除：参考主README的故障排除章节

### 按主题分类

#### 🏗️ 架构设计
- [`README_SYSTEM.md`](README_SYSTEM.md) - 系统整体架构
- [`README_REFACTOR.md`](README_REFACTOR.md) - 三层架构设计

#### 🔄 版本演进
- [`README_REFACTOR.md`](README_REFACTOR.md) - v2.0重构
- [`README_MODIFICATIONS.md`](README_MODIFICATIONS.md) - v2.1修改

#### 🛠️ 工具和解决方案
- [`README_CODE_CLEANING.md`](README_CODE_CLEANING.md) - 代码清理工具

## 📋 文档使用建议

### 新手入门路径
1. 先阅读主README了解系统概况
2. 阅读 [`README_SYSTEM.md`](README_SYSTEM.md) 了解架构
3. 根据需要查阅具体的技术文档

### 问题解决路径
1. 查看主README的故障排除章节
2. 如果是代码格式问题，参考 [`README_CODE_CLEANING.md`](README_CODE_CLEANING.md)
3. 如果是架构相关问题，参考 [`README_SYSTEM.md`](README_SYSTEM.md)

### 开发维护路径
1. 了解重构历史：[`README_REFACTOR.md`](README_REFACTOR.md)
2. 了解最新修改：[`README_MODIFICATIONS.md`](README_MODIFICATIONS.md)
3. 掌握清理工具：[`README_CODE_CLEANING.md`](README_CODE_CLEANING.md)

## 🔗 相关链接

- [主README](../README.md) - 系统主要文档
- [测试目录](../tests/) - 测试文件和示例
- [源代码](../) - 质控系统源代码

## 📝 文档维护

### 更新原则
- 每次重大修改后更新相关文档
- 保持文档与代码的同步
- 及时补充新功能的说明

### 贡献指南
- 发现文档问题请提交Issue
- 欢迎提交文档改进的Pull Request
- 遵循现有的文档格式和风格

---

**文档索引版本**：1.0  
**最后更新**：2025-08-05  
**维护者**：Medical QA Agent Team
