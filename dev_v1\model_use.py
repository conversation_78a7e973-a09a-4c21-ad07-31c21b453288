# -*- coding: utf-8 -*-
from openai import OpenAI
import concurrent.futures
import json
from config import *

# 单独调用llm
def llm_use(system_prompt, user_prompt, model_config):
    # 创建OpenAI客户端
    client = OpenAI(
        api_key=model_config["api_key"],
        base_url=model_config["request_url"]
    )
    
    try:
        response = client.chat.completions.create(
            model=model_config["model"],
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            temperature=model_config["default_temperature"],
            max_tokens=model_config["default_max_tokens"],
            top_p=model_config["default_top_p"],
            stream=model_config["streaming"]
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"请求错误: {e}")
        return None

# 并发调用LLM的函数
def llm_use_concurrent(tasks):
    """
    并发调用LLM API
    :param tasks: 包含多个任务的列表，每个任务是一个字典，包含system_prompt和user_prompt
    :return: 包含每个任务结果的列表，顺序与输入任务列表相同
    """

    def process_task(task):
        system_prompt = task['system_prompt']
        user_prompt = task['user_prompt']
        model_config = task.get('model_config', qwen_32B_config)
        llm_output = llm_use(system_prompt, user_prompt, model_config)
        # 返回包含原始任务信息和LLM结果的新字典
        return {**task, 'llm_result': llm_output}

    ordered_results = []
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # 提交所有任务并按顺序存储future对象
        # future_objects列表保持提交顺序
        future_objects = [executor.submit(process_task, task) for task in tasks]

        # 按提交顺序获取结果，以保证最终结果列表的顺序与输入任务列表一致
        # 对每个future调用.result()会阻塞直到该特定future完成
        # 由于我们按序遍历future_objects，ordered_results将按照tasks的顺序填充
        for future in future_objects:
            ordered_results.append(future.result())

    return ordered_results

if __name__ == '__main__':
    print([llm_use("你是一个有用的助手。", "你好吗？", qwen_32B_config)])