# 规则ID: rule_4084bb5a
# 描述: 检查出院医嘱是否完整包含药名、剂量、用法、带药总量及随访要求

import logging

def check_rule(medical_record):
    """
    检查出院医嘱是否缺失必要信息
    返回True表示存在缺失，False表示完整
    """
    try:
        # 获取出院医嘱部分
        discharge_instructions = medical_record.get('discharge_instructions', {})
        
        # 检查必要字段
        required_fields = {
            'drugs': '未写明药名',
            'dosage': '未写明剂量',
            'usage': '未写明用法',
            'total_amount': '未写明带药总量',
            'follow_up': '缺少随访要求',
            'precautions': '缺少注意事项'
        }
        
        # 检查每个必要字段是否存在且非空
        for field, error_msg in required_fields.items():
            if not discharge_instructions.get(field):
                logging.warning(f"出院医嘱缺失: {error_msg}")
                return True
                
        return False
                
    except Exception as e:
        logging.error(f"规则执行异常: {str(e)}")
        return True

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 完整的出院医嘱
    test_case_1 = {
        'discharge_instructions': {
            'drugs': '阿司匹林',
            'dosage': '100mg',
            'usage': '每日两次',
            'total_amount': '100片',
            'follow_up': '两周后复诊',
            'precautions': '避免空腹服用'
        }
    }
    assert check_rule(test_case_1) == False, "测试用例1失败"
    
    # 测试用例2: 缺少药名
    test_case_2 = {
        'discharge_instructions': {
            'dosage': '100mg',
            'usage': '每日两次',
            'total_amount': '100片',
            'follow_up': '两周后复诊',
            'precautions': '避免空腹服用'
        }
    }
    assert check_rule(test_case_2) == True, "测试用例2失败"
    
    # 测试用例3: 缺少多个字段
    test_case_3 = {
        'discharge_instructions': {
            'drugs': '阿司匹林',
            'dosage': '100mg',
            'follow_up': '两周后复诊'
        }
    }
    assert check_rule(test_case_3) == True, "测试用例3失败"
    
    # 测试用例4: 空的出院医嘱
    test_case_4 = {
        'discharge_instructions': {}
    }
    assert check_rule(test_case_4) == True, "测试用例4失败"
    
    # 测试用例5: 非字典结构输入
    test_case_5 = "文本格式的病历数据"
    assert check_rule(test_case_5) == True, "测试用例5失败"
    
    print("所有测试用例通过")