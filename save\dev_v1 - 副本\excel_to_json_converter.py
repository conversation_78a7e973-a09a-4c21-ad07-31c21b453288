#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel规则导入模块
功能：
1. 读取Excel文件中的质控规则数据
2. 将原始规则数据规范化为标准JSON格式
3. 按病历文本名称进行规则分类
4. 为每个规则生成唯一标识名称
"""

import pandas as pd
import json
import os
from typing import Dict, List, Any
import uuid
from datetime import datetime


class ExcelToJsonConverter:
    """Excel规则转换器类"""
    
    def __init__(self):
        self.rules_data = []
        self.categorized_rules = {}
        
    def read_excel_file(self, file_path: str, sheet_name: str = None) -> pd.DataFrame:
        """
        读取Excel文件
        
        Args:
            file_path (str): Excel文件路径
            sheet_name (str): 工作表名称，默认为None（读取第一个工作表）
            
        Returns:
            pd.DataFrame: 读取的数据
        """
        try:
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                df = pd.read_excel(file_path)
            
            print(f"成功读取Excel文件: {file_path}")
            print(f"数据形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return None
    
    def normalize_rule_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        将原始规则数据规范化为标准JSON格式
        
        Args:
            df (pd.DataFrame): 原始Excel数据
            
        Returns:
            List[Dict]: 规范化后的规则数据列表
        """
        normalized_rules = []
        
        for index, row in df.iterrows():
            # 生成唯一规则ID（使用短格式：rule_前缀+8位十六进制）
            rule_id = f"rule_{uuid.uuid4().hex[:8]}"
            
            # 根据图片中的表格结构创建规则对象
            rule = {
                "rule_id": rule_id,
                "category": str(row.get('分类', '')).strip() if pd.notna(row.get('分类', '')) else '',
                "rule_type": str(row.get('规则类型', '')).strip() if pd.notna(row.get('规则类型', '')) else '',
                "document_type": str(row.get('文书类型', '')).strip() if pd.notna(row.get('文书类型', '')) else '',
                "document_type_english": str(row.get('文书类型英语', '')).strip() if pd.notna(row.get('文书类型英语', '')) else '',
                "rule_name": str(row.get('规则名称', '')).strip() if pd.notna(row.get('规则名称', '')) else '',
                "score": int(row.get('扣分', 0)) if pd.notna(row.get('扣分', 0)) else 0,
                "description": str(row.get('描述', '')).strip() if pd.notna(row.get('描述', '')) else '',
                "created_time": datetime.now().isoformat(),
                "status": "active"
            }
            
            # 处理可能的其他列名变体
            for col in df.columns:
                col_lower = col.lower()
                if '分类' in col or 'category' in col_lower:
                    rule['category'] = str(row[col]).strip() if pd.notna(row[col]) else ''
                elif '规则类型' in col or 'rule_type' in col_lower:
                    rule['rule_type'] = str(row[col]).strip() if pd.notna(row[col]) else ''
                elif '文书类型' in col or 'document_type' in col_lower:
                    rule['document_type'] = str(row[col]).strip() if pd.notna(row[col]) else ''
                elif '规则名称' in col or 'rule_name' in col_lower:
                    rule['rule_name'] = str(row[col]).strip() if pd.notna(row[col]) else ''
                elif '扣分' in col or 'score' in col_lower:
                    rule['score'] = int(row[col]) if pd.notna(row[col]) else 0
            
            normalized_rules.append(rule)
        
        self.rules_data = normalized_rules
        print(f"成功规范化 {len(normalized_rules)} 条规则")
        
        return normalized_rules
    
    def categorize_rules_by_document_type(self) -> Dict[str, List[Dict]]:
        """
        按病历文本名称进行规则分类
        
        Returns:
            Dict[str, List[Dict]]: 按文档类型分类的规则字典
        """
        categorized = {}
        
        for rule in self.rules_data:
            doc_type = rule.get('document_type', '未分类')
            
            if doc_type not in categorized:
                categorized[doc_type] = []
            
            categorized[doc_type].append(rule)
        
        self.categorized_rules = categorized
        
        print(f"规则分类完成，共 {len(categorized)} 个文档类型:")
        for doc_type, rules in categorized.items():
            print(f"  - {doc_type}: {len(rules)} 条规则")
        
        return categorized
    
    def generate_unique_rule_names(self) -> None:
        """
        为每个规则生成唯一标识名称
        """
        rule_name_counts = {}
        
        for rule in self.rules_data:
            base_name = rule.get('rule_name', '未命名规则')
            
            # 统计相同规则名称的数量
            if base_name in rule_name_counts:
                rule_name_counts[base_name] += 1
                unique_name = f"{base_name}_{rule_name_counts[base_name]}"
            else:
                rule_name_counts[base_name] = 0
                unique_name = base_name
            
            rule['unique_rule_name'] = unique_name
        
        print("规则唯一名称生成完成")
    
    def save_to_json(self, output_path: str, data_type: str = 'all') -> bool:
        """
        保存数据到JSON文件
        
        Args:
            output_path (str): 输出文件路径
            data_type (str): 数据类型 ('all', 'categorized', 'rules_only')
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if data_type == 'all':
                output_data = {
                    "metadata": {
                        "total_rules": len(self.rules_data),
                        "categories_count": len(self.categorized_rules),
                        "export_time": datetime.now().isoformat(),
                        "version": "1.0"
                    },
                    "rules": self.rules_data
                }
            elif data_type == 'categorized':
                output_data = self.categorized_rules
            else:  # rules_only
                output_data = self.rules_data
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"数据已保存到: {output_path}")
            return True
            
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
            return False
    
    def process_excel_file(self, excel_path: str, output_dir: str = None, sheet_name: str = None) -> bool:
        """
        完整处理Excel文件的主方法
        
        Args:
            excel_path (str): Excel文件路径
            output_dir (str): 输出目录，默认为当前目录
            sheet_name (str): 工作表名称
            
        Returns:
            bool: 处理是否成功
        """
        print(f"开始处理Excel文件: {excel_path}")
        
        # 1. 读取Excel文件
        df = self.read_excel_file(excel_path, sheet_name)
        if df is None:
            return False
        
        # 2. 规范化数据
        self.normalize_rule_data(df)
        
        # 3. 按文档类型分类
        self.categorize_rules_by_document_type()
        
        # 4. 生成唯一规则名称
        self.generate_unique_rule_names()
        
        # 5. 保存到JSON文件
        if output_dir is None:
            # 默认保存到dev_v1/rule文件夹
            current_dir = os.path.dirname(os.path.abspath(__file__))
            output_dir = os.path.join(current_dir, 'rule')
            
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 使用病历文件的英文名作为文件名
        english_name = None
        if self.rules_data:
            english_name = self.rules_data[0].get('document_type_english', '')
        
        if not english_name:
            # 如果没有英文名，使用原文件名
            base_name = os.path.splitext(os.path.basename(excel_path))[0]
        else:
            # 清理英文名，移除特殊字符
            base_name = english_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
        
        # 只保存一个完整的JSON文件
        output_path = os.path.join(output_dir, f"{base_name}.json")
        self.save_to_json(output_path, 'all')
        
        print("Excel文件处理完成！")
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            "total_rules": len(self.rules_data),
            "document_types": list(self.categorized_rules.keys()),
            "rules_by_category": {}
        }
        
        for doc_type, rules in self.categorized_rules.items():
            stats["rules_by_category"][doc_type] = len(rules)
        
        return stats


def main():
    """主函数 - 示例用法"""
    # 创建转换器实例
    converter = ExcelToJsonConverter()
    
    # 设置文件路径
    excel_file = "../doc/首次病程录质控.xlsx"  # 根据实际文件路径调整
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"文件不存在: {excel_file}")
        print("请确保Excel文件路径正确")
        return
    
    # 处理Excel文件
    success = converter.process_excel_file(excel_file)
    
    if success:
        # 显示统计信息
        stats = converter.get_statistics()
        print("\n=== 处理统计 ===")
        print(f"总规则数: {stats['total_rules']}")
        print(f"文档类型数: {len(stats['document_types'])}")
        print("\n各类型规则数量:")
        for doc_type, count in stats['rules_by_category'].items():
            print(f"  {doc_type}: {count} 条")
    else:
        print("处理失败")


if __name__ == "__main__":
    main()