# Rule ID: rule_4e774c85
# Description: 首次病程缺病例特点

def check_rule(medical_record):
    """
    检查首次病程记录中是否缺少"病例特点"段落
    返回True表示存在问题（缺少该段落），False表示符合要求
    """
    try:
        # 参数类型校验
        if not isinstance(medical_record, str):
            return True
            
        # 检查是否包含"病例特点"关键词
        if "病例特点" not in medical_record:
            return True
            
        return False
            
    except Exception as e:
        # 捕获所有异常并返回错误状态
        return True

if __name__ == "__main__":
    # 测试用例
    test_cases = [
        # 包含正常情况
        ("首次病程记录：患者男性，50岁。病例特点：发热、咳嗽...", False),
        # 缺失情况
        ("首次病程记录：患者男性，50岁。主诉：发热...", True),
        # 非字符串输入
        (12345, True),
        # None值
        (None, True),
        # 完全不包含关键词
        ("患者自述近期身体不适，建议进一步检查", True),
        # 包含但位置靠后
        ("患者入院后完善检查，生命体征平稳。病例特点：...", False)
    ]
    
    for i, (input_data, expected) in enumerate(test_cases):
        result = check_rule(input_data)
        print(f"Test case {i+1}: {'Passed' if result == expected else 'Failed'} "
              f"(Input: {input_data!r}, Expected: {expected}, Got: {result})")