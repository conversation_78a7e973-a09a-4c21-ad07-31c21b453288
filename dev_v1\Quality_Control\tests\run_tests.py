"""
出院小结质控功能测试文件
该文件用于测试 dev_v1/Quality_Control/quality_controller.py 中的质控函数
使用 dev_v1/data/discharge_summary_test_data.json 中的数据作为测试输入
"""

import os
import sys
import json
import logging
from typing import Dict, Any, Tuple

# 添加项目路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 从 tests 目录向上找到 Quality_Control 目录
quality_control_dir = os.path.dirname(current_dir)
# 从 Quality_Control 目录向上找到 dev_v1 目录
dev_v1_dir = os.path.dirname(quality_control_dir)
# 从 dev_v1 目录向上找到项目根目录
project_root = os.path.dirname(dev_v1_dir)

# 添加项目根目录和 dev_v1 目录到 Python 路径
sys.path.insert(0, project_root)
sys.path.insert(0, dev_v1_dir)

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 导入质控控制器
try:
    # 尝试多种导入方式
    try:
        from Quality_Control.quality_controller import quality_control
        logger.info("成功导入质控控制器模块 (方式1)")
    except ImportError:
        from dev_v1.Quality_Control.quality_controller import quality_control
        logger.info("成功导入质控控制器模块 (方式2)")
except ImportError as e:
    logger.error(f"导入质控控制器模块失败: {e}")
    logger.error(f"当前工作目录: {os.getcwd()}")
    logger.error(f"Python路径: {sys.path}")
    sys.exit(1)


def load_discharge_summary_test_data() -> Dict[str, Any]:
    """
    从测试数据文件加载出院小结测试数据

    Returns:
        dict: 测试数据字典
    """
    try:
        # 构建测试数据文件路径
        test_data_path = os.path.join(dev_v1_dir, 'data', 'discharge_summary_test_data.json')

        logger.info(f"加载测试数据文件: {test_data_path}")

        # 检查文件是否存在
        if not os.path.exists(test_data_path):
            raise FileNotFoundError(f"测试数据文件不存在: {test_data_path}")

        # 读取JSON文件
        with open(test_data_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)

        logger.info(f"成功加载测试数据，包含 {len(test_data.get('Data', []))} 条记录")
        return test_data

    except Exception as e:
        logger.error(f"加载测试数据失败: {e}")
        raise


def test_discharge_summary_quality_control():
    """
    测试出院小结质控功能

    该函数调用质控控制器对测试数据进行质控检查，
    并输出函数的输入参数和输出结果
    """
    try:
        logger.info("开始测试出院小结质控功能")

        # 加载测试数据
        test_data = load_discharge_summary_test_data()
        print(test_data)
        data_records = test_data.get('Data', [])

        if not data_records:
            logger.warning("测试数据为空，无法进行测试")
            return

        # 测试每条记录
        for i, record in enumerate(data_records, 1):
            logger.info(f"测试第 {i} 条记录")

            # 输出函数输入参数
            print(f"\n{'='*60}")
            print(f"测试用例 {i}: 患者 {record.get('Patient', {}).get('PatientName', 'Unknown')}")
            print(f"{'='*60}")
            print("函数输入参数:")
            print(f"  type: 'DischargeSummary'")
            print(f"  data: {json.dumps(record, ensure_ascii=False, indent=2)}")

            # 调用质控函数
            try:
                processed_data, quality_info = quality_control("DischargeSummary", record)

                # 输出函数输出结果
                print(f"\n函数输出结果:")
                print(f"  processed_data: {json.dumps(processed_data, ensure_ascii=False, indent=2)}")
                print(f"  quality_info: {json.dumps(quality_info, ensure_ascii=False, indent=2)}")

                logger.info(f"第 {i} 条记录质控测试完成")

            except Exception as e:
                logger.error(f"第 {i} 条记录质控测试失败: {e}")
                print(f"\n函数执行异常: {e}")

        logger.info("出院小结质控功能测试完成")

    except Exception as e:
        logger.error(f"测试出院小结质控功能失败: {e}")
        raise


if __name__ == "__main__":
    print("1")
    """
    主函数：运行所有测试
    """
    try:
        logger.info("开始运行质控功能测试")

        # 运行出院小结质控测试
        test_discharge_summary_quality_control()

        logger.info("所有测试完成")

    except Exception as e:
        logger.error(f"测试运行失败: {e}")
        sys.exit(1)