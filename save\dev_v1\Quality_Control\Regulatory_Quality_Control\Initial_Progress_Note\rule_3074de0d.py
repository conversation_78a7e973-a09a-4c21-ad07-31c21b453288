"""
Rule ID: rule_3074de0d
Description: 首次病程缺诊断依据
"""

def check_rule(medical_record):
    """
    检查首次病程记录是否缺少诊断依据
    返回True表示存在问题，False表示无问题
    """
    try:
        # 获取首次病程记录字段
        initial_course = medical_record.get('initial_course', '')
        
        # 判断是否为空或仅含空白字符
        if not str(initial_course).strip():
            return True
            
        # 检查是否包含诊断依据相关关键词（可根据实际需求扩展）
        if '诊断依据' not in str(initial_course):
            return True
            
        return False
            
    except (AttributeError, KeyError, TypeError):
        # 处理非字典类型输入或字段缺失情况
        return True

if __name__ == "__main__":
    # 测试用例1: 正常情况
    test1 = {'initial_course': '根据患者病史及检查结果，诊断依据充分...'}
    print(check_rule(test1))  # 预期输出: False
    
    # 测试用例2: 缺失字段
    test2 = {'medical_history': '...'}
    print(check_rule(test2))  # 预期输出: True
    
    # 测试用例3: 空字符串
    test3 = {'initial_course': ''}
    print(check_rule(test3))  # 预期输出: True
    
    # 测试用例4: 非字符串类型
    test4 = {'initial_course': None}
    print(check_rule(test4))  # 预期输出: True
    
    # 测试用例5: 缺失关键词
    test5 = {'initial_course': '患者主诉...'}
    print(check_rule(test5))  # 预期输出: True