# -*- coding: utf-8 -*-
"""
简单的质控系统测试
"""
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))

from quality_control_main import QualityControlMain

def simple_test():
    """简单测试质控系统的基本功能"""
    print("=" * 60)
    print("质控系统基本功能测试")
    print("=" * 60)
    
    # 初始化质控系统
    qc_main = QualityControlMain()
    
    # 测试数据
    test_record = {
        "patient_id": "TEST001",
        "patient_name": "测试患者",
        "content": "测试病历内容",
        "admission_date": "2024-08-01",
        "discharge_date": "2024-08-05"
    }
    
    print("\n1. 测试支持的文档类型:")
    supported_types = qc_main.get_supported_document_types()
    for chinese, english in supported_types["mappings"].items():
        print(f"   • {chinese} → {english}")
    
    print("\n2. 测试支持的质控类型:")
    qc_types = qc_main.get_supported_quality_control_types()
    for type_key, type_name in qc_types["types"].items():
        print(f"   • {type_name} ({type_key})")
    
    print("\n3. 测试内涵质控:")
    try:
        result = qc_main.run_content_quality_control("出院记录", test_record)
        if "error" in result:
            print(f"   ❌ 失败: {result['error']}")
        else:
            print(f"   ✅ 成功: 规则数量 {result.get('metadata', {}).get('total_rules', 0)}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    print("\n4. 测试规则质控:")
    try:
        result = qc_main.run_rule_quality_control("出院记录", test_record)
        if "error" in result:
            print(f"   ❌ 失败: {result['error']}")
        else:
            print(f"   ✅ 成功: 规则数量 {result.get('metadata', {}).get('total_rules', 0)}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    print("\n5. 测试综合质控:")
    try:
        result = qc_main.run_integrated_quality_control("出院记录", test_record)
        if "error" in result:
            print(f"   ❌ 失败: {result['error']}")
        else:
            metadata = result.get('metadata', {})
            print(f"   ✅ 成功:")
            print(f"      总规则数: {metadata.get('total_rules', 0)}")
            print(f"      规则质控: {metadata.get('regulatory_count', 0)}条")
            print(f"      内涵质控: {metadata.get('connotation_count', 0)}条")
            
            # 生成摘要
            summary = qc_main.get_quality_control_summary(result)
            print(f"      综合得分: {summary.get('overall_score', 0):.1f}/100")
            print(f"      质量等级: {summary.get('quality_grade', '未知')}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    simple_test()
