import json
import os

def read_rules_count(json_file_path):
    """
    读取JSON文件并打印其中的rules总数
    
    Args:
        json_file_path (str): JSON文件的路径
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(json_file_path):
            print(f"错误：文件 {json_file_path} 不存在")
            return
        
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # 获取rules总数
        if 'metadata' in data and 'total_rules' in data['metadata']:
            total_rules = data['metadata']['total_rules']
            print(f"从metadata中获取的rules总数: {total_rules}")
        
        # 也可以直接统计rules数组的长度
        if 'rules' in data:
            rules_count = len(data['rules'])
            print(f"直接统计rules数组的长度: {rules_count}")
        else:
            print("错误：JSON文件中没有找到'rules'字段")
            
    except json.JSONDecodeError as e:
        print(f"错误：JSON文件格式不正确 - {e}")
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")

if __name__ == "__main__":
    # JSON文件路径
    json_file_path = r"d:\Documents\Program\CCOS\LLMs2\Hospital\Medical_QA_Agent\dev_v1\rule\Initial_Progress_Note.json"
    
    print(f"正在读取文件: {json_file_path}")
    read_rules_count(json_file_path)