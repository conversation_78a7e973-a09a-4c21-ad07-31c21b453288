# -*- coding: utf-8 -*-
"""
医疗记录列表生成器
功能：
1. 读取Excel文件中的质控评分表数据
2. 提取医疗文档相关数据的6个列
3. 统计4个特定类别的唯一值
4. 使用LLM生成英文翻译
5. 按文书类型生成JSON文件
"""

import pandas as pd
import json
import os
import sys
import logging
import uuid
from typing import Dict, List, Any, Tuple
from datetime import datetime
from collections import Counter
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill

# 添加父目录到路径以导入model_use
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model_use import llm_use

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('medical_record_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MedicalRecordListGenerator:
    """医疗记录列表生成器类"""

    def __init__(self):
        self.raw_data = None
        self.processed_data = []
        self.category_stats = {}
        self.translations = {}
        self.required_columns = [
            '规则类型',      # Rule Type
            '分类',          # Classification
            '所属项目',      # Belonging Project
            '文书类型',      # Document Type
            '规则内容',      # Rule Content
            '扣分'           # Deduction Points
        ]
        self.translation_categories = [
            '规则类型',      # Rule Type
            '分类',          # Classification
            '所属项目',      # Belonging Project
            '文书类型'       # Document Type
        ]

    def read_excel_file(self, file_path: str) -> bool:
        """
        读取Excel文件

        Args:
            file_path (str): Excel文件路径

        Returns:
            bool: 读取是否成功
        """
        try:
            logger.info(f"开始读取Excel文件: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return False

            # 读取Excel文件
            self.raw_data = pd.read_excel(file_path)

            logger.info(f"成功读取Excel文件")
            logger.info(f"数据形状: {self.raw_data.shape}")
            logger.info(f"原始列名: {list(self.raw_data.columns)}")

            return True

        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            return False

    def validate_and_extract_data(self) -> bool:
        """
        验证并提取所需的6个列数据

        Returns:
            bool: 验证和提取是否成功
        """
        try:
            if self.raw_data is None:
                logger.error("原始数据为空，请先读取Excel文件")
                return False

            # 处理重复的列名问题（分类.1）
            columns_mapping = {}
            available_columns = list(self.raw_data.columns)

            logger.info(f"Excel文件中的所有列名: {available_columns}")

            # 特殊处理：将"分类"字段映射到Excel中的"分类.1"列
            for required_col in self.required_columns:
                if required_col == '分类':
                    # 使用"分类.1"列而不是"分类"列
                    if '分类.1' in available_columns:
                        columns_mapping[required_col] = '分类.1'
                        logger.info(f"将'{required_col}'字段映射到Excel列'{columns_mapping[required_col]}'")
                    else:
                        logger.error(f"缺少必需的列: 分类.1")
                        return False
                else:
                    if required_col in available_columns:
                        columns_mapping[required_col] = required_col
                    else:
                        logger.error(f"缺少必需的列: {required_col}")
                        return False

            logger.info("所有必需的列都存在")
            logger.info(f"列映射: {columns_mapping}")

            # 提取所需列的数据，使用映射后的列名
            extracted_data = pd.DataFrame()
            for logical_col, excel_col in columns_mapping.items():
                extracted_data[logical_col] = self.raw_data[excel_col]

            # 清理数据：移除空行和无效数据
            extracted_data = extracted_data.dropna(subset=['文书类型', '规则内容'])

            # 转换为字典列表
            self.processed_data = extracted_data.to_dict('records')

            logger.info(f"成功提取 {len(self.processed_data)} 条有效记录")

            return True

        except Exception as e:
            logger.error(f"数据验证和提取失败: {e}")
            return False

    def generate_category_statistics(self) -> bool:
        """
        统计4个特定类别的唯一值

        Returns:
            bool: 统计是否成功
        """
        try:
            if not self.processed_data:
                logger.error("处理后的数据为空，请先提取数据")
                return False

            logger.info("开始生成类别统计")

            # 统计每个类别的唯一值
            for category in self.translation_categories:
                unique_values = set()
                for record in self.processed_data:
                    value = str(record.get(category, '')).strip()
                    if value and value != 'nan':
                        unique_values.add(value)

                self.category_stats[category] = {
                    'unique_values': sorted(list(unique_values)),
                    'count': len(unique_values)
                }

                logger.info(f"{category}: {len(unique_values)} 个唯一值")

            # 记录详细统计信息
            for category, stats in self.category_stats.items():
                logger.info(f"\n{category} 的唯一值:")
                for value in stats['unique_values'][:10]:  # 只显示前10个
                    logger.info(f"  - {value}")
                if len(stats['unique_values']) > 10:
                    logger.info(f"  ... 还有 {len(stats['unique_values']) - 10} 个")

            return True

        except Exception as e:
            logger.error(f"生成类别统计失败: {e}")
            return False

    def generate_english_translations(self, model_config: Dict[str, Any]) -> bool:
        """
        使用LLM生成英文翻译

        Args:
            model_config (Dict[str, Any]): 模型配置参数

        Returns:
            bool: 翻译是否成功
        """
        try:
            if not self.category_stats:
                logger.error("类别统计为空，请先生成统计")
                return False

            logger.info("开始生成英文翻译")

            # 为每个类别的每个唯一值生成翻译
            for category, stats in self.category_stats.items():
                logger.info(f"正在翻译 {category} 类别的 {stats['count']} 个值")

                category_translations = {}

                for i, chinese_term in enumerate(stats['unique_values'], 1):
                    try:
                        logger.info(f"  正在翻译 ({i}/{len(stats['unique_values'])}): {chinese_term}")

                        # 创建翻译提示
                        system_prompt = """你是一个专业的医疗术语翻译专家，专门负责将中文医疗术语准确翻译成英文。

翻译要求：
1. 使用标准的医疗英语术语和国际通用表达
2. 采用标题大小写格式（Title Case），如：Medical Record, Quality Control
3. 保持术语的专业性和准确性
4. 翻译应简洁明了，避免冗余词汇
5. 对于医疗文档类型，使用标准的医疗文档英文名称
6. 只返回英文翻译结果，不包含任何解释或其他内容

示例：
- 入院记录 → Admission Record
- 质控规则 → Quality Control Rule
- 病历管理 → Medical Record Management"""

                        user_prompt = f"请将以下中文医疗术语翻译成英文，使用标题大小写格式，只返回英文翻译：{chinese_term}"

                        # 调用LLM进行翻译，添加超时处理
                        try:
                            import time
                            import threading

                            # 使用线程实现超时控制
                            result = [None]
                            exception = [None]

                            def translation_thread():
                                try:
                                    result[0] = llm_use(system_prompt, user_prompt, model_config)
                                except Exception as e:
                                    exception[0] = e

                            start_time = time.time()
                            thread = threading.Thread(target=translation_thread)
                            thread.daemon = True
                            thread.start()
                            thread.join(timeout=30)  # 30秒超时
                            end_time = time.time()

                            if thread.is_alive():
                                logger.error(f"  ⏰ 翻译超时 (>30秒): {chinese_term}")
                                english_translation = None
                            elif exception[0]:
                                logger.error(f"  ❌ API调用错误: {exception[0]}")
                                english_translation = None
                            else:
                                english_translation = result[0]
                                logger.info(f"    ⏱️ API调用耗时: {end_time - start_time:.2f}秒")

                        except Exception as api_error:
                            logger.error(f"  ❌ 翻译线程错误: {api_error}")
                            english_translation = None

                        if english_translation:
                            # 清理翻译结果并确保标题大小写格式
                            english_translation = english_translation.strip().strip('"').strip("'")
                            # 简单的标题大小写处理
                            english_translation = self._format_title_case(english_translation)
                            category_translations[chinese_term] = english_translation
                            logger.info(f"  ✅ {chinese_term} -> {english_translation}")
                        else:
                            logger.warning(f"  ⚠️ 翻译失败: {chinese_term}")
                            category_translations[chinese_term] = chinese_term  # 使用原文作为备选

                        # 添加短暂延迟避免API频率限制
                        time.sleep(0.5)

                    except Exception as e:
                        logger.error(f"  ❌ 翻译 '{chinese_term}' 时出错: {e}")
                        category_translations[chinese_term] = chinese_term  # 使用原文作为备选

                self.translations[category] = category_translations

            logger.info("英文翻译生成完成")
            return True

        except Exception as e:
            logger.error(f"生成英文翻译失败: {e}")
            return False

    def _format_title_case(self, text: str) -> str:
        """
        格式化为标题大小写

        Args:
            text (str): 原始文本

        Returns:
            str: 格式化后的文本
        """
        # 不需要大写的小词
        small_words = {'a', 'an', 'and', 'as', 'at', 'but', 'by', 'for', 'if', 'in',
                      'nor', 'of', 'on', 'or', 'so', 'the', 'to', 'up', 'yet'}

        words = text.split()
        if not words:
            return text

        # 第一个词总是大写
        formatted_words = [words[0].capitalize()]

        # 处理其余的词
        for word in words[1:]:
            if word.lower() in small_words:
                formatted_words.append(word.lower())
            else:
                formatted_words.append(word.capitalize())

        return ' '.join(formatted_words)

    def generate_json_files(self, output_dir: str = "Medical_Record_List_Json") -> bool:
        """
        按文书类型生成JSON文件

        Args:
            output_dir (str): 输出目录

        Returns:
            bool: 生成是否成功
        """
        try:
            if not self.processed_data:
                logger.error("处理后的数据为空，请先提取数据")
                return False

            if not self.translations:
                logger.error("翻译数据为空，请先生成翻译")
                return False

            logger.info(f"开始生成JSON文件到目录: {os.path.abspath(output_dir)}")

            # 确保输出目录存在
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                logger.info(f"创建JSON输出目录: {output_dir}")

            # 按文书类型分组数据
            document_type_groups = {}
            for record in self.processed_data:
                doc_type = str(record.get('文书类型', '')).strip()
                if doc_type and doc_type != 'nan':
                    if doc_type not in document_type_groups:
                        document_type_groups[doc_type] = []
                    document_type_groups[doc_type].append(record)

            logger.info(f"找到 {len(document_type_groups)} 种文书类型")

            # 为每种文书类型生成JSON文件
            generated_files = []
            for doc_type, records in document_type_groups.items():
                try:
                    # 获取文书类型的英文翻译作为文件名
                    english_filename = self.translations.get('文书类型', {}).get(doc_type, doc_type)

                    # 清理文件名，移除特殊字符
                    safe_filename = self._clean_filename(english_filename)
                    json_filename = f"{safe_filename}.json"
                    json_filepath = os.path.join(output_dir, json_filename)

                    # 准备JSON数据
                    json_data = {
                        "metadata": {
                            "document_type_chinese": doc_type,
                            "document_type_english": english_filename,
                            "total_records": len(records),
                            "generated_time": datetime.now().isoformat(),
                            "version": "1.0"
                        },
                        "translations": {
                            category: translations for category, translations in self.translations.items()
                        },
                        "records": []
                    }

                    # 处理每条记录
                    for record in records:
                        processed_record = {
                            "rule_id": f"rule_{uuid.uuid4().hex[:8]}",  # 添加唯一的规则ID（短格式）
                            "rule_type_chinese": record.get('规则类型', ''),
                            "rule_type_english": self.translations.get('规则类型', {}).get(
                                str(record.get('规则类型', '')), str(record.get('规则类型', ''))
                            ),
                            "classification_chinese": record.get('分类', ''),
                            "classification_english": self.translations.get('分类', {}).get(
                                str(record.get('分类', '')), str(record.get('分类', ''))
                            ),
                            "belonging_project_chinese": record.get('所属项目', ''),
                            "belonging_project_english": self.translations.get('所属项目', {}).get(
                                str(record.get('所属项目', '')), str(record.get('所属项目', ''))
                            ),
                            "document_type_chinese": record.get('文书类型', ''),
                            "document_type_english": self.translations.get('文书类型', {}).get(
                                str(record.get('文书类型', '')), str(record.get('文书类型', ''))
                            ),
                            "rule_content": record.get('规则内容', ''),
                            "deduction_points": record.get('扣分', 0)
                        }
                        json_data["records"].append(processed_record)

                    # 保存JSON文件
                    with open(json_filepath, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)

                    generated_files.append(json_filepath)
                    logger.info(f"生成JSON文件: {json_filepath} ({len(records)} 条记录)")

                except Exception as e:
                    logger.error(f"生成文书类型 '{doc_type}' 的JSON文件时出错: {e}")

            logger.info(f"成功生成 {len(generated_files)} 个JSON文件")
            return True

        except Exception as e:
            logger.error(f"生成JSON文件失败: {e}")
            return False

    def _clean_filename(self, filename: str) -> str:
        """
        清理文件名，移除特殊字符

        Args:
            filename (str): 原始文件名

        Returns:
            str: 清理后的文件名
        """
        # 移除或替换特殊字符
        import re
        # 保留字母、数字、下划线、连字符和空格
        cleaned = re.sub(r'[^\w\s-]', '', filename)
        # 将空格替换为下划线
        cleaned = re.sub(r'\s+', '_', cleaned)
        # 移除多余的下划线
        cleaned = re.sub(r'_+', '_', cleaned)
        # 移除开头和结尾的下划线
        cleaned = cleaned.strip('_')

        return cleaned if cleaned else "unknown_document_type"

    def generate_excel_statistics(self, output_dir: str) -> bool:
        """
        生成Excel统计表

        Args:
            output_dir (str): 输出目录

        Returns:
            bool: 生成是否成功
        """
        try:
            if not self.translations:
                logger.error("翻译数据为空，请先生成翻译")
                return False

            logger.info("开始生成Excel统计表")

            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_filename = f"category_translations_{timestamp}.xlsx"
            excel_filepath = os.path.join(output_dir, excel_filename)

            # 创建Excel工作簿
            workbook = openpyxl.Workbook()

            # 删除默认工作表
            workbook.remove(workbook.active)

            # 创建汇总工作表
            summary_sheet = workbook.create_sheet("汇总信息", 0)
            self._create_summary_sheet(summary_sheet)

            # 为每个类别创建工作表
            for category, translations in self.translations.items():
                sheet_name = category.replace('/', '_')  # 避免工作表名称中的特殊字符
                sheet = workbook.create_sheet(sheet_name)
                self._create_category_sheet(sheet, category, translations)

            # 保存Excel文件
            workbook.save(excel_filepath)
            logger.info(f"Excel统计表已保存到: {excel_filepath}")

            return True

        except Exception as e:
            logger.error(f"生成Excel统计表失败: {e}")
            return False

    def _create_summary_sheet(self, sheet):
        """创建汇总信息工作表"""
        # 设置标题
        sheet['A1'] = "医疗记录翻译统计汇总"
        sheet['A1'].font = Font(size=16, bold=True)
        sheet['A1'].alignment = Alignment(horizontal='center')
        sheet.merge_cells('A1:C1')

        # 设置生成时间
        sheet['A3'] = "生成时间:"
        sheet['B3'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 设置总记录数
        sheet['A4'] = "总记录数:"
        sheet['B4'] = len(self.processed_data) if self.processed_data else 0

        # 设置类别统计标题
        sheet['A6'] = "类别统计"
        sheet['A6'].font = Font(size=14, bold=True)

        # 设置表头
        headers = ["序号", "类别名称", "唯一值数量"]
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=7, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal='center')

        # 填充数据
        row = 8
        for idx, (category, translations) in enumerate(self.translations.items(), 1):
            sheet.cell(row=row, column=1, value=idx)
            sheet.cell(row=row, column=2, value=category)
            sheet.cell(row=row, column=3, value=len(translations))
            row += 1

        # 调整列宽
        sheet.column_dimensions['A'].width = 8
        sheet.column_dimensions['B'].width = 20
        sheet.column_dimensions['C'].width = 15

    def _create_category_sheet(self, sheet, category: str, translations: Dict[str, str]):
        """创建类别工作表"""
        # 设置标题
        sheet['A1'] = f"{category} - 翻译对照表"
        sheet['A1'].font = Font(size=14, bold=True)
        sheet['A1'].alignment = Alignment(horizontal='center')
        sheet.merge_cells('A1:C1')

        # 设置表头
        headers = ["序号", "中文原文", "英文翻译"]
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=3, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal='center')

        # 按中文原文排序
        sorted_items = sorted(translations.items(), key=lambda x: x[0])

        # 填充数据
        for idx, (chinese, english) in enumerate(sorted_items, 1):
            sheet.cell(row=idx+3, column=1, value=idx)
            sheet.cell(row=idx+3, column=2, value=chinese)
            sheet.cell(row=idx+3, column=3, value=english)

        # 调整列宽
        sheet.column_dimensions['A'].width = 8
        sheet.column_dimensions['B'].width = 30
        sheet.column_dimensions['C'].width = 40

    def generate_markdown_statistics(self, output_dir: str) -> bool:
        """
        生成Markdown统计表

        Args:
            output_dir (str): 输出目录

        Returns:
            bool: 生成是否成功
        """
        try:
            if not self.translations:
                logger.error("翻译数据为空，请先生成翻译")
                return False

            logger.info("开始生成Markdown统计表")

            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            md_filename = f"category_translations_{timestamp}.md"
            md_filepath = os.path.join(output_dir, md_filename)

            # 生成Markdown内容
            md_content = self._generate_markdown_content()

            # 保存Markdown文件
            with open(md_filepath, 'w', encoding='utf-8') as f:
                f.write(md_content)

            logger.info(f"Markdown统计表已保存到: {md_filepath}")
            return True

        except Exception as e:
            logger.error(f"生成Markdown统计表失败: {e}")
            return False

    def _generate_markdown_content(self) -> str:
        """生成Markdown内容"""
        lines = []

        # 文件标题和生成时间
        lines.append("# 医疗记录翻译统计表")
        lines.append("")
        lines.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")

        # 统计摘要
        lines.append("## 统计摘要")
        lines.append("")
        lines.append(f"- **总记录数**: {len(self.processed_data) if self.processed_data else 0}")
        lines.append(f"- **翻译类别数**: {len(self.translations)}")
        lines.append("")

        # 类别统计表
        lines.append("### 类别统计")
        lines.append("")
        lines.append("| 序号 | 类别名称 | 唯一值数量 |")
        lines.append("|------|----------|------------|")

        for idx, (category, translations) in enumerate(self.translations.items(), 1):
            lines.append(f"| {idx} | {category} | {len(translations)} |")

        lines.append("")

        # 为每个类别创建详细表格
        for category, translations in self.translations.items():
            lines.append(f"## {category} - 翻译对照表")
            lines.append("")
            lines.append("| 中文原文 | 英文翻译 |")
            lines.append("|----------|----------|")

            # 按中文原文排序
            sorted_items = sorted(translations.items(), key=lambda x: x[0])

            for chinese, english in sorted_items:
                # 转义Markdown特殊字符
                chinese_escaped = chinese.replace('|', '\\|').replace('\n', ' ')
                english_escaped = english.replace('|', '\\|').replace('\n', ' ')
                lines.append(f"| {chinese_escaped} | {english_escaped} |")

            lines.append("")

        return '\n'.join(lines)

    def process_excel_file(self, excel_path: str, model_config: Dict[str, Any],
                          output_dir: str = ".") -> bool:
        """
        完整处理Excel文件的主方法

        Args:
            excel_path (str): Excel文件路径
            model_config (Dict[str, Any]): 模型配置参数
            output_dir (str): 输出目录

        Returns:
            bool: 处理是否成功
        """
        try:
            logger.info("=" * 60)
            logger.info("开始处理医疗记录Excel文件")
            logger.info("=" * 60)

            # Step 1: 读取Excel文件
            logger.info("Step 1: 读取Excel文件")
            if not self.read_excel_file(excel_path):
                return False

            # Step 2: 验证并提取数据
            logger.info("Step 2: 验证并提取数据")
            if not self.validate_and_extract_data():
                return False

            # Step 3: 生成类别统计
            logger.info("Step 3: 生成类别统计")
            if not self.generate_category_statistics():
                return False

            # Step 4: 生成英文翻译
            logger.info("Step 4: 生成英文翻译")
            if not self.generate_english_translations(model_config):
                return False

            # Step 5: 生成JSON文件
            logger.info("Step 5: 生成JSON文件")
            json_output_dir = "Medical_Record_List_Json"
            if not self.generate_json_files(json_output_dir):
                return False

            # Step 6: 生成Excel统计表
            logger.info("Step 6: 生成Excel统计表")
            if not self.generate_excel_statistics(output_dir):
                logger.warning("Excel统计表生成失败，但不影响主流程")

            # Step 7: 生成Markdown统计表
            logger.info("Step 7: 生成Markdown统计表")
            if not self.generate_markdown_statistics(output_dir):
                logger.warning("Markdown统计表生成失败，但不影响主流程")

            logger.info("=" * 60)
            logger.info("医疗记录Excel文件处理完成！")
            logger.info("=" * 60)

            return True

        except Exception as e:
            logger.error(f"处理Excel文件时出现错误: {e}")
            return False

    def get_processing_summary(self) -> Dict[str, Any]:
        """
        获取处理摘要信息

        Returns:
            Dict[str, Any]: 处理摘要
        """
        summary = {
            "total_records": len(self.processed_data) if self.processed_data else 0,
            "category_statistics": {},
            "translation_statistics": {},
            "document_types": []
        }

        # 类别统计
        for category, stats in self.category_stats.items():
            summary["category_statistics"][category] = {
                "unique_count": stats["count"],
                "sample_values": stats["unique_values"][:5]  # 前5个示例
            }

        # 翻译统计
        for category, translations in self.translations.items():
            summary["translation_statistics"][category] = len(translations)

        # 文书类型
        if self.processed_data:
            doc_types = set()
            for record in self.processed_data:
                doc_type = str(record.get('文书类型', '')).strip()
                if doc_type and doc_type != 'nan':
                    doc_types.add(doc_type)
            summary["document_types"] = sorted(list(doc_types))

        return summary


def _find_excel_file() -> str:
    """
    智能查找Excel文件路径

    Returns:
        str: Excel文件的正确路径，如果找不到则返回空字符串
    """
    excel_filename = "附件1. 质控评分表v2.0.xlsx"

    # 可能的路径列表，按优先级排序
    possible_paths = [
        # 从主目录运行
        f"doc/{excel_filename}",
        f"../doc/{excel_filename}",
        f"../../doc/{excel_filename}",

        # 从tests目录运行
        f"../doc/{excel_filename}",
        f"../../doc/{excel_filename}",
        f"../../../doc/{excel_filename}",

        # 从更深层目录运行
        f"../../../../doc/{excel_filename}",
        f"../../../../../doc/{excel_filename}",

        # 绝对路径尝试（基于当前工作目录）
        os.path.join(os.getcwd(), "doc", excel_filename),
        os.path.join(os.path.dirname(os.getcwd()), "doc", excel_filename),
        os.path.join(os.path.dirname(os.path.dirname(os.getcwd())), "doc", excel_filename),

        # 直接在当前目录查找
        excel_filename,
        f"./{excel_filename}",
    ]

    # 尝试每个可能的路径
    for path in possible_paths:
        if os.path.exists(path):
            return os.path.abspath(path)

    return ""

def _get_default_excel_path() -> Tuple[str, str]:
    """
    获取默认的Excel文件路径和显示路径

    Returns:
        Tuple[str, str]: (实际路径, 显示给用户的路径)
    """
    # 首先尝试智能查找
    found_path = _find_excel_file()
    if found_path:
        # 计算相对路径用于显示
        try:
            rel_path = os.path.relpath(found_path)
            return found_path, rel_path
        except ValueError:
            return found_path, found_path

    # 如果找不到，返回最可能的默认路径
    current_dir = os.getcwd()
    if "tests" in current_dir.lower():
        default_display = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    elif "Medical_Record_List" in current_dir:
        default_display = "../../doc/附件1. 质控评分表v2.0.xlsx"
    else:
        default_display = "doc/附件1. 质控评分表v2.0.xlsx"

    return default_display, default_display

def get_user_input() -> Tuple[str, Dict[str, Any], str]:
    """
    获取用户输入

    Returns:
        Tuple[str, Dict[str, Any], str]: (文件路径, 模型配置, 输出目录)
    """
    print("=" * 60)
    print("医疗记录列表生成器")
    print("=" * 60)

    # 获取默认Excel文件路径
    default_path, display_path = _get_default_excel_path()

    # 获取Excel文件路径
    while True:
        if os.path.exists(default_path):
            prompt = f"请输入Excel文件路径 (默认: {display_path}): "
            status = "✅ 文件已找到"
        else:
            prompt = f"请输入Excel文件路径 (建议: {display_path}): "
            status = "⚠️ 默认路径不存在，请手动输入"

        print(f"当前工作目录: {os.getcwd()}")
        print(f"状态: {status}")

        file_path = input(prompt).strip()
        if not file_path:
            file_path = default_path

        # 尝试智能查找用户输入的文件
        if not os.path.exists(file_path):
            # 如果用户输入的路径不存在，尝试智能查找
            if os.path.basename(file_path) == "附件1. 质控评分表v2.0.xlsx":
                smart_path = _find_excel_file()
                if smart_path:
                    print(f"💡 智能找到文件: {smart_path}")
                    file_path = smart_path

        if os.path.exists(file_path):
            print(f"✅ 使用文件: {os.path.abspath(file_path)}")
            break
        else:
            print(f"❌ 文件不存在: {file_path}")
            print("\n可能的解决方案:")
            print("1. 检查文件名是否正确")
            print("2. 尝试以下路径之一:")

            # 显示一些可能的路径建议
            suggestions = [
                "doc/附件1. 质控评分表v2.0.xlsx",
                "../doc/附件1. 质控评分表v2.0.xlsx",
                "../../doc/附件1. 质控评分表v2.0.xlsx",
                "../../../doc/附件1. 质控评分表v2.0.xlsx"
            ]

            for i, suggestion in enumerate(suggestions, 1):
                exists_status = "✅" if os.path.exists(suggestion) else "❌"
                print(f"   {exists_status} {suggestion}")

            print("3. 使用绝对路径")
            print("请重新输入正确的文件路径\n")

    # 获取模型配置
    print("\n可用的模型配置:")
    print("1. glm_code_config (GLM-4.5-Flash) [推荐]")
    print("2. deepseek_r1_config (DeepSeek R1)")
    print("3. deepseek_v3_config (DeepSeek V3)")
    print("4. kimi_k2_config (Kimi K2)")
    print("5. qwen_30B_config (Qwen 30B)")
    print("6. qwen_32B_config (Qwen 32B)")

    while True:
        choice = input("请选择模型配置 (1-6, 默认: 1): ").strip()
        if not choice:
            choice = "1"

        if choice == "1":
            from config import glm_code_config
            model_config = glm_code_config
            break
        elif choice == "2":
            from config import deepseek_r1_config
            model_config = deepseek_r1_config
            break
        elif choice == "3":
            from config import deepseek_v3_config
            model_config = deepseek_v3_config
            break
        elif choice == "4":
            from config import kimi_k2_config
            model_config = kimi_k2_config
            break
        elif choice == "5":
            from config import qwen_30B_config
            model_config = qwen_30B_config
            break
        elif choice == "6":
            from config import qwen_32B_config
            model_config = qwen_32B_config
            break
        else:
            print("无效选择，请输入 1-6")

    # 获取输出目录
    output_dir = input("请输入输出目录 (默认: 当前目录): ").strip()
    if not output_dir:
        output_dir = "."

    return file_path, model_config, output_dir


def main():
    """主函数"""
    try:
        # 获取用户输入
        file_path, model_config, output_dir = get_user_input()

        # 创建生成器实例
        generator = MedicalRecordListGenerator()

        # 处理Excel文件
        success = generator.process_excel_file(file_path, model_config, output_dir)

        if success:
            # 显示处理摘要
            summary = generator.get_processing_summary()
            print("\n" + "=" * 60)
            print("处理摘要")
            print("=" * 60)
            print(f"总记录数: {summary['total_records']}")
            print(f"文书类型数: {len(summary['document_types'])}")

            print("\n类别统计:")
            for category, stats in summary['category_statistics'].items():
                print(f"  {category}: {stats['unique_count']} 个唯一值")

            print("\n翻译统计:")
            for category, count in summary['translation_statistics'].items():
                print(f"  {category}: {count} 个翻译")

            print(f"\n文书类型列表:")
            for doc_type in summary['document_types'][:10]:  # 显示前10个
                print(f"  - {doc_type}")
            if len(summary['document_types']) > 10:
                print(f"  ... 还有 {len(summary['document_types']) - 10} 个")

            print(f"\n📁 文件保存位置:")
            print(f"   JSON文件: {os.path.abspath('Medical_Record_List_Json')}")
            print(f"   统计文件: {os.path.abspath(output_dir)}")
            print("✅ 处理完成！")
        else:
            print("处理失败，请查看日志了解详细信息")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        logger.error(f"主函数执行出错: {e}")
        print(f"执行出错: {e}")


if __name__ == "__main__":
    main()