import pandas as pd
import os

def read_medical_data_worksheet(file_path):
    """读取Excel文件中的病历数据元表工作表"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 '{file_path}' 未找到")
            return None

        # 指定要读取的工作表名称
        worksheet_name = "病历数据元表"

        # 读取指定的工作表
        df = pd.read_excel(file_path, sheet_name=worksheet_name)

        # 显示基本信息
        print(f"文件：{file_path}")
        print(f"工作表：{worksheet_name}")
        print(f"数据形状：{df.shape} (行数, 列数)")
        print(f"列名：{list(df.columns)}")

        # 将数据转换为列表格式（排除表头）
        data_list = []
        for _, row in df.iterrows():
            row_data = row.tolist()
            data_list.append(row_data)

        print(f"\n数据列表结构（共 {len(data_list)} 行数据）：")
        for i, row in enumerate(data_list):
            print(f"第 {i+1} 行：{row}")

        return data_list

    except ValueError as e:
        if "Worksheet named" in str(e):
            print(f"错误：工作表 '{worksheet_name}' 不存在")
            print("请检查Excel文件中的工作表名称")
        else:
            print(f"数值错误：{e}")
        return None
    except Exception as e:
        print(f"读取Excel文件时发生错误：{e}")
        return None



if __name__ == "__main__":
    # 相对于脚本位置的文件路径
    excel_file = "###质控字段规范.xlsx"

    # 读取并显示病历数据元表
    data = read_medical_data_worksheet(excel_file)