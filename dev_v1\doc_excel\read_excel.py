import pandas as pd
import os
import sys

# 添加上级目录到路径以导入model_use模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model_use import llm_use
from config import qwen_32B_config

def read_medical_data_worksheet(file_path):
    """读取Excel文件中的病历数据元表工作表"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 '{file_path}' 未找到")
            return None

        # 指定要读取的工作表名称
        worksheet_name = "病历数据元表"

        # 读取指定的工作表
        df = pd.read_excel(file_path, sheet_name=worksheet_name)

        # 显示基本信息
        print(f"文件：{file_path}")
        print(f"工作表：{worksheet_name}")
        print(f"数据形状：{df.shape} (行数, 列数)")
        print(f"列名：{list(df.columns)}")

        # 将数据转换为列表格式（排除表头）
        data_list = []
        for _, row in df.iterrows():
            row_data = row.tolist()
            data_list.append(row_data)

        # print(f"\n数据列表结构（共 {len(data_list)} 行数据）：")
        # for i, row in enumerate(data_list):
        #     print(f"第 {i+1} 行：{row}")

        return data_list

    except ValueError as e:
        if "Worksheet named" in str(e):
            print(f"错误：工作表 '{worksheet_name}' 不存在")
            print("请检查Excel文件中的工作表名称")
        else:
            print(f"数值错误：{e}")
        return None
    except Exception as e:
        print(f"读取Excel文件时发生错误：{e}")
        return None

def translate_chinese_fields(row):
    """
    翻译单行数据中索引位置1的中文字段为英文

    参数:
        row (list): 单行数据，例如 [5421, '幻嗅', nan, 'EMR_005421', 'EMR_1874041593066037249']

    返回:
        list: 翻译后的单行数据，保持原始结构
    """
    # 创建原始行数据的副本，避免修改原始数据
    translated_row = row.copy()

    # 检查索引位置1是否存在
    if len(row) <= 1:
        return translated_row

    # 获取索引位置1的值
    chinese_field = row[1]

    # 检查是否为None
    if chinese_field is None:
        return translated_row

    # 转换为字符串并检查是否为有效的中文内容
    chinese_text = str(chinese_field).strip()

    # 跳过NaN、空字符串或无效值
    if (chinese_text.lower() == 'nan' or
        chinese_text == '' or
        chinese_text.lower() == 'none'):
        return translated_row

    try:
        # 构建翻译提示词
        system_prompt = "你是一个专业的医学术语翻译助手。请将中文医学术语准确翻译为英文。只返回翻译结果，不要添加任何解释。"
        user_prompt = f"请将以下中文医学术语翻译为英文：{chinese_text}"

        # 调用LLM进行翻译
        translated_text = llm_use(system_prompt, user_prompt, qwen_32B_config)

        # 如果翻译成功，更新索引位置1的值
        if translated_text and translated_text.strip():
            translated_row[1] = translated_text.strip()

    except Exception as e:
        # 翻译失败时保持原文，不抛出异常
        print(f"翻译过程中发生错误：{e}，保持原文：{chinese_text}")

    return translated_row



if __name__ == "__main__":
    # 相对于脚本位置的文件路径
    excel_file = "###质控字段规范.xlsx"

    # 读取并显示病历数据元表
    data = read_medical_data_worksheet(excel_file)

    print(data[0])
