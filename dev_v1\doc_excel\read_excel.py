import pandas as pd
import os

def read_excel_file(file_path):
    """Read Excel file and display contents"""
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found")
            return None
        
        # Read Excel file
        df = pd.read_excel(file_path)
        
        # Display basic info
        print(f"File: {file_path}")
        print(f"Shape: {df.shape} (rows, columns)")
        print(f"Columns: {list(df.columns)}")
        print("\nData:")
        print(df.to_string())
        
        return df
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

if __name__ == "__main__":
    # File path relative to script location
    excel_file = "###质控字段规范.xlsx"
    
    # Read and display the Excel file
    data = read_excel_file(excel_file)
