import pandas as pd
import os
import sys

# 添加上级目录到路径以导入model_use模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model_use import llm_use
from config import qwen_32B_config

def read_medical_data_worksheet(file_path):
    """读取Excel文件中的病历数据元表工作表"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 '{file_path}' 未找到")
            return None

        # 指定要读取的工作表名称
        worksheet_name = "病历数据元表"

        # 读取指定的工作表
        df = pd.read_excel(file_path, sheet_name=worksheet_name)

        # 显示基本信息
        print(f"文件：{file_path}")
        print(f"工作表：{worksheet_name}")
        print(f"数据形状：{df.shape} (行数, 列数)")
        print(f"列名：{list(df.columns)}")

        # 将数据转换为列表格式（排除表头）
        data_list = []
        for _, row in df.iterrows():
            row_data = row.tolist()
            data_list.append(row_data)

        # print(f"\n数据列表结构（共 {len(data_list)} 行数据）：")
        # for i, row in enumerate(data_list):
        #     print(f"第 {i+1} 行：{row}")

        return data_list

    except ValueError as e:
        if "Worksheet named" in str(e):
            print(f"错误：工作表 '{worksheet_name}' 不存在")
            print("请检查Excel文件中的工作表名称")
        else:
            print(f"数值错误：{e}")
        return None
    except Exception as e:
        print(f"读取Excel文件时发生错误：{e}")
        return None

def translate_chinese_fields(row):
    """
    翻译单行数据中索引位置1的中文字段为英文

    参数:
        row (list): 单行数据，例如 [5421, '幻嗅', nan, 'EMR_005421', 'EMR_1874041593066037249']

    返回:
        list: 翻译后的单行数据，保持原始结构
    """
    # 创建原始行数据的副本，避免修改原始数据
    translated_row = row.copy()

    # 检查索引位置1是否存在
    if len(row) <= 1:
        return translated_row

    # 获取索引位置1的值
    chinese_field = row[1]

    # 检查是否为None
    if chinese_field is None:
        return translated_row

    # 转换为字符串并检查是否为有效的中文内容
    chinese_text = str(chinese_field).strip()

    # 跳过NaN、空字符串或无效值
    if (chinese_text.lower() == 'nan' or
        chinese_text == '' or
        chinese_text.lower() == 'none'):
        return translated_row

    try:
        # 构建翻译提示词
        system_prompt = "你是一个专业的医学术语翻译助手。请将中文医学术语准确翻译为英文。只返回翻译结果，不要添加任何解释。"
        user_prompt = f"请将以下中文医学术语翻译为英文：{chinese_text}"

        # 调用LLM进行翻译
        translated_text = llm_use(system_prompt, user_prompt, qwen_32B_config)

        # 如果翻译成功，更新索引位置2的值
        if translated_text and translated_text.strip():
            translated_row[2] = translated_text.strip()

    except Exception as e:
        # 翻译失败时保持原文，不抛出异常
        print(f"翻译过程中发生错误：{e}，保持原文：{chinese_text}")

    return translated_row

def batch_translate_and_save(file_path, worksheet_name="病历数据元表"):
    """
    批量翻译Excel文件中的中文字段并保存回原文件

    参数:
        file_path (str): Excel文件路径
        worksheet_name (str): 工作表名称
    """
    try:
        print(f"开始读取文件：{file_path}")

        # 读取所有工作表以保持其他工作表不变
        with pd.ExcelFile(file_path) as xls:
            all_sheets = {}
            for sheet_name in xls.sheet_names:
                all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)

        # 检查目标工作表是否存在
        if worksheet_name not in all_sheets:
            print(f"错误：工作表 '{worksheet_name}' 不存在")
            return False

        # 获取目标工作表数据
        df = all_sheets[worksheet_name]
        print(f"工作表 '{worksheet_name}' 读取成功，共 {len(df)} 行数据")

        # 将DataFrame转换为列表格式进行翻译
        data_list = []
        for _, row in df.iterrows():
            data_list.append(row.tolist())

        print("开始批量翻译中文字段...")
        translated_data = []

        # 批量翻译每一行
        for i, row in enumerate(data_list):
            print(f"正在处理第 {i+1}/{len(data_list)} 行")
            translated_row = translate_chinese_fields(row)
            translated_data.append(translated_row)

        # 将翻译后的数据转换回DataFrame
        translated_df = pd.DataFrame(translated_data, columns=df.columns)
        all_sheets[worksheet_name] = translated_df

        print("翻译完成，开始保存文件...")

        # 保存所有工作表到Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            for sheet_name, sheet_df in all_sheets.items():
                sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"文件保存成功：{file_path}")
        print(f"工作表 '{worksheet_name}' 已更新，共处理 {len(translated_data)} 行数据")
        return True

    except Exception as e:
        print(f"批量翻译和保存过程中发生错误：{e}")
        return False



if __name__ == "__main__":
    # 相对于脚本位置的文件路径
    excel_file = "###质控字段规范.xlsx"

    print("="*60)
    print("医学术语翻译工具")
    print("="*60)

    # 读取并显示病历数据元表
    print("步骤1：读取Excel文件...")
    data = read_medical_data_worksheet(excel_file)

    if data:
        print(f"成功读取 {len(data)} 行数据")

        # 显示第一行作为示例
        if len(data) > 0:
            print(f"示例数据（第1行）：{data[0]}")

        print("\n步骤2：开始批量翻译和保存...")
        print("-" * 40)

        # 执行批量翻译和保存
        success = batch_translate_and_save(excel_file)

        if success:
            print("\n" + "="*60)
            print("✅ 翻译和保存完成！")
            print("✅ 所有中文医学术语已翻译为英文")
            print("✅ 文件已成功保存")
            print("="*60)
        else:
            print("\n" + "="*60)
            print("❌ 翻译或保存过程中出现错误")
            print("="*60)
    else:
        print("❌ 未能读取到数据，程序终止")
    