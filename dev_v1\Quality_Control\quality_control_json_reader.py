import json
import os


def read_discharge_summary_records(json_file_path, filter_type):
    """
    读取并解析出院记录JSON文件中的records部分，根据入参筛选并输出相应的记录

    参数:
        json_file_path (str): JSON文件的路径
        filter_type (str): 筛选类型，可选值：
            - "规则": 输出type为"规则"和"规则和内涵"的记录
            - "内涵": 输出type为"内涵"和"规则和内涵"的记录

    功能说明:
        - 当入参为"规则"时，输出type为"规则"和"规则和内涵"的所有记录
        - 当入参为"内涵"时，输出type为"内涵"和"规则和内涵"的所有记录
        - 包含错误处理机制，处理文件不存在、JSON格式错误等异常情况

    返回:
        None: 函数直接打印结果，不返回值
    """
    try:
        # 验证入参
        if filter_type not in ["规则", "内涵"]:
            print(f"错误: 无效的筛选类型 '{filter_type}'，请使用 '规则' 或 '内涵'")
            return

        # 检查文件是否存在
        if not os.path.exists(json_file_path):
            print(f"错误: 文件 '{json_file_path}' 不存在")
            return

        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # 检查JSON结构是否包含records字段
        if 'records' not in data:
            print("错误: JSON文件中缺少'records'字段")
            return
        
        records = data['records']
        
        # 检查records是否为列表
        if not isinstance(records, list):
            print("错误: 'records'字段不是列表格式")
            return
        
        if len(records) == 0:
            print("提示: records列表为空")
            return

        # 根据入参确定需要筛选的type类型
        if filter_type == "规则":
            target_types = ["规则", "规则和内涵"]
        else:  # filter_type == "内涵"
            target_types = ["内涵", "规则和内涵"]

        print(f"筛选类型: {filter_type}")
        print(f"目标类型: {target_types}")
        print(f"开始处理 {len(records)} 条记录:")
        print("-" * 80)

        # 统计不同type的数量和筛选后的数量
        type_counts = {}
        filtered_count = 0

        # 遍历每条记录
        for i, record in enumerate(records, 1):
            try:
                # 检查必要字段是否存在
                if 'type' not in record:
                    print(f"警告: 第{i}条记录缺少'type'字段，跳过处理")
                    continue
                
                if 'rule_content' not in record:
                    print(f"警告: 第{i}条记录缺少'rule_content'字段，跳过处理")
                    continue
                
                record_type = record['type']
                rule_content = record['rule_content']
                rule_id = record.get('rule_id', f'未知ID_{i}')
                
                # 统计type数量
                type_counts[record_type] = type_counts.get(record_type, 0) + 1

                # 根据入参筛选记录
                if record_type in target_types:
                    filtered_count += 1
                    print(f"记录 {i} ({rule_id}):")
                    print(f"  类型: {record_type}")
                    print(f"  内容: {rule_content}")

                    # 显示额外信息
                    if 'rule_type_chinese' in record:
                        print(f"  规则类型: {record['rule_type_chinese']}")
                    if 'deduction_points' in record:
                        print(f"  扣分: {record['deduction_points']}")

                    print("-" * 40)
                

            except Exception as e:
                print(f"错误: 处理第{i}条记录时发生异常: {str(e)}")
                continue

        # 打印统计信息
        print("\n处理完成，统计信息:")
        print(f"筛选条件: {filter_type} (目标类型: {target_types})")
        print(f"符合条件的记录: {filtered_count} 条")
        print("\n所有记录类型统计:")
        for type_name, count in type_counts.items():
            print(f"  {type_name}: {count} 条")
        print(f"  总计: {sum(type_counts.values())} 条")
        
    except FileNotFoundError:
        print(f"错误: 无法找到文件 '{json_file_path}'")
    except json.JSONDecodeError as e:
        print(f"错误: JSON文件格式错误 - {str(e)}")
    except PermissionError:
        print(f"错误: 没有权限读取文件 '{json_file_path}'")
    except Exception as e:
        print(f"错误: 发生未预期的异常 - {str(e)}")


if __name__ == "__main__":
    # 示例用法（仅在直接运行此脚本时执行）
    # 注意: 这里的路径需要根据实际情况调整
    sample_path = "../rule_type/rule_type_json/DischargeSummary_type.json"

    print("示例调用1 - 筛选规则类型:")
    read_discharge_summary_records(sample_path, "规则")

    print("\n" + "="*80 + "\n")

    print("示例调用2 - 筛选内涵类型:")
    read_discharge_summary_records(sample_path, "内涵")
