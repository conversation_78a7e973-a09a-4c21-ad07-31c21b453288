# -*- coding: utf-8 -*-
"""
验证JSON文件的保存位置配置
"""

import sys
import os
import shutil

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator
from config import deepseek_v3_config

def test_json_output_location():
    """验证JSON文件的保存位置配置"""
    print("=" * 80)
    print("验证JSON文件的保存位置配置")
    print("=" * 80)
    
    # 设置参数 - 使用智能路径检测
    from medical_record_list_generator import _find_excel_file
    excel_path = _find_excel_file()
    if not excel_path:
        excel_path = "../../doc/附件1. 质控评分表v2.0.xlsx"  # 从主目录运行时的路径
    model_config = deepseek_v3_config
    
    print(f"📂 当前工作目录: {os.getcwd()}")
    print(f"📄 当前脚本位置: {os.path.abspath(__file__)}")
    print(f"📁 脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
    
    # 检查当前配置
    print("\n" + "=" * 60)
    print("Step 1: 检查当前配置")
    print("=" * 60)
    
    # 创建生成器实例来检查默认配置
    generator = MedicalRecordListGenerator()
    
    # 检查 generate_json_files 方法的默认参数
    import inspect
    sig = inspect.signature(generator.generate_json_files)
    default_output_dir = sig.parameters['output_dir'].default
    print(f"✅ generate_json_files() 默认输出目录: '{default_output_dir}'")
    
    # 预期的JSON输出目录
    expected_json_dir = "Medical_Record_List_Json"
    current_dir = os.getcwd()
    expected_absolute_path = os.path.join(current_dir, expected_json_dir)
    
    print(f"✅ 预期JSON输出目录: '{expected_json_dir}'")
    print(f"✅ 预期绝对路径: {expected_absolute_path}")
    
    # 验证配置是否正确
    config_correct = (default_output_dir == expected_json_dir)
    print(f"✅ 配置正确性: {'通过' if config_correct else '失败'}")
    
    # 清理可能存在的测试目录
    test_json_dir = expected_json_dir
    if os.path.exists(test_json_dir):
        shutil.rmtree(test_json_dir)
        print(f"🧹 清理已存在的测试目录: {test_json_dir}")
    
    # 执行基础步骤
    print("\n" + "=" * 60)
    print("Step 2: 执行基础数据处理步骤")
    print("=" * 60)
    
    if not generator.read_excel_file(excel_path):
        print("❌ Excel文件读取失败")
        return False
    
    if not generator.validate_and_extract_data():
        print("❌ 数据验证和提取失败")
        return False
    
    if not generator.generate_category_statistics():
        print("❌ 类别统计生成失败")
        return False
    
    print("✅ 基础数据处理步骤完成")
    
    # 创建简单的翻译数据
    print("\n" + "=" * 60)
    print("Step 3: 创建测试翻译数据")
    print("=" * 60)
    
    test_translations = {
        '规则类型': {'时效性': 'Timeliness'},
        '分类': {'手术科室': 'Surgical Department'},
        '所属项目': {'病程记录': 'Progress Record'},
        '文书类型': {'24小时内入院死亡记录': '24-Hour Admission and Death Record'}
    }
    
    generator.translations = test_translations
    print("✅ 测试翻译数据创建完成")
    
    # 测试JSON文件生成和保存位置
    print("\n" + "=" * 60)
    print("Step 4: 测试JSON文件生成和保存位置")
    print("=" * 60)
    
    print("🔧 使用默认参数调用 generate_json_files()...")
    success = generator.generate_json_files()  # 使用默认参数
    
    if not success:
        print("❌ JSON文件生成失败")
        return False
    
    print("✅ JSON文件生成成功")
    
    # 验证保存位置
    print("\n" + "=" * 60)
    print("Step 5: 验证保存位置")
    print("=" * 60)
    
    # 检查目录是否在正确位置创建
    if os.path.exists(test_json_dir):
        print(f"✅ JSON输出目录已创建: {os.path.abspath(test_json_dir)}")
        
        # 检查目录结构
        json_files = [f for f in os.listdir(test_json_dir) if f.endswith('.json')]
        print(f"✅ 生成了 {len(json_files)} 个JSON文件")
        
        # 显示前5个文件及其完整路径
        print("📄 生成的JSON文件示例:")
        for i, filename in enumerate(json_files[:5], 1):
            file_path = os.path.join(test_json_dir, filename)
            abs_file_path = os.path.abspath(file_path)
            file_size = os.path.getsize(file_path)
            print(f"   {i}. {filename}")
            print(f"      路径: {abs_file_path}")
            print(f"      大小: {file_size} bytes")
        
        if len(json_files) > 5:
            print(f"   ... 还有 {len(json_files) - 5} 个文件")
        
        # 验证路径结构
        print(f"\n📁 验证目录结构:")
        script_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(script_dir)  # Medical_Record_List目录
        expected_json_path = os.path.join(parent_dir, expected_json_dir)
        actual_json_path = os.path.abspath(test_json_dir)
        
        print(f"   脚本所在目录: {parent_dir}")
        print(f"   预期JSON目录: {expected_json_path}")
        print(f"   实际JSON目录: {actual_json_path}")
        
        # 检查是否在正确的相对位置
        path_correct = os.path.exists(expected_json_path)
        print(f"   路径结构正确: {'✅ 是' if path_correct else '❌ 否'}")
        
        # 检查是否没有在其他位置创建JSON文件
        other_locations = [
            ".",
            "../",
            "../../",
            "tests/"
        ]
        
        print(f"\n🔍 检查其他位置是否有JSON文件:")
        unwanted_json_found = False
        for location in other_locations:
            if os.path.exists(location):
                files_in_location = [f for f in os.listdir(location) 
                                   if f.endswith('.json') and 'category_translations' not in f]
                if files_in_location:
                    print(f"   ⚠️ 在 {os.path.abspath(location)} 发现JSON文件: {len(files_in_location)}个")
                    unwanted_json_found = True
                else:
                    print(f"   ✅ {os.path.abspath(location)}: 无意外JSON文件")
        
        if not unwanted_json_found:
            print("   ✅ 没有在其他位置发现意外的JSON文件")
        
    else:
        print(f"❌ JSON输出目录未创建: {test_json_dir}")
        return False
    
    # 测试完整流程
    print("\n" + "=" * 60)
    print("Step 6: 测试完整流程中的路径配置")
    print("=" * 60)
    
    # 清理之前的测试文件
    if os.path.exists(test_json_dir):
        shutil.rmtree(test_json_dir)
        print(f"🧹 清理测试文件")
    
    # 使用 process_excel_file 方法测试完整流程
    print("🔧 测试 process_excel_file() 方法中的JSON路径配置...")
    
    # 重新创建生成器实例
    full_generator = MedicalRecordListGenerator()
    
    # 模拟完整流程的前几步
    if (full_generator.read_excel_file(excel_path) and 
        full_generator.validate_and_extract_data() and 
        full_generator.generate_category_statistics()):
        
        full_generator.translations = test_translations
        
        # 检查 process_excel_file 中的 json_output_dir 设置
        print(f"✅ process_excel_file() 中设置的 json_output_dir: 'Medical_Record_List_Json'")
        
        # 直接调用JSON生成部分
        json_output_dir = "Medical_Record_List_Json"
        if full_generator.generate_json_files(json_output_dir):
            print("✅ 完整流程中的JSON文件生成成功")
            
            if os.path.exists(json_output_dir):
                print(f"✅ JSON文件保存到正确位置: {os.path.abspath(json_output_dir)}")
            else:
                print("❌ JSON文件未保存到预期位置")
                return False
        else:
            print("❌ 完整流程中的JSON文件生成失败")
            return False
    
    # 总结
    print("\n" + "=" * 80)
    print("🎉 JSON文件保存位置验证完成！")
    print("=" * 80)
    
    verification_results = {
        "默认参数配置": default_output_dir == expected_json_dir,
        "目录创建成功": os.path.exists(test_json_dir),
        "文件生成成功": len(json_files) > 0 if os.path.exists(test_json_dir) else False,
        "路径结构正确": path_correct if os.path.exists(test_json_dir) else False,
        "无意外文件": not unwanted_json_found
    }
    
    all_passed = all(verification_results.values())
    
    print("📋 验证结果:")
    for test_name, result in verification_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
    
    if all_passed:
        print("\n🎉 JSON文件保存位置配置正确！")
        print("   - 默认输出目录: Medical_Record_List_Json")
        print("   - 相对路径: ./Medical_Record_List_Json/")
        print("   - 绝对路径: [脚本运行目录]/Medical_Record_List_Json/")
        print("   - 目录结构符合预期")
        print("   - 没有在其他位置创建意外文件")
    
    return all_passed

if __name__ == "__main__":
    test_json_output_location()
