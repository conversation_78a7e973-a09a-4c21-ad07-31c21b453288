#!/usr/bin/env python3
"""
简单的导入测试
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("开始测试导入...")

try:
    from dev_v1.Quality_Control.quality_controller import quality_control
    print("✅ 导入成功!")
    
    # 测试函数是否可调用
    print(f"函数签名: {quality_control.__doc__}")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
