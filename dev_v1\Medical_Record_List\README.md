# 医疗记录列表生成器 (Medical Record List Generator)

一个用于处理医疗质控评分表数据的Python工具，支持数据提取、分类统计、翻译生成和多格式输出。

## 🚀 快速开始

### 推荐使用：更新版本（无LLM依赖）⭐

```bash
# 直接运行更新版本
python medical_record_list_generator_updated.py
```

**简单三步：**
1. 输入Excel文件路径（支持智能路径检测）
2. 输入输出目录（默认为当前目录）
3. 自动生成结果文件

## 📁 项目结构

```
dev_v1/Medical_Record_List/
├── medical_record_list_generator_updated.py   # 🌟 推荐版本（预定义翻译）
├── medical_record_list_generator.py           # 原始版本（LLM翻译）
├── category_translations.md                   # 标准翻译对照表
├── README.md                                  # 项目说明文档
├── Medical_Record_List_Json/                  # JSON输出目录
└── tests/                                     # 测试文件目录
```

## 🔄 版本对比

| 特性 | 更新版本 ⭐ | 原始版本 |
|------|------------|----------|
| **翻译方式** | 预定义映射表 | LLM动态生成 |
| **处理速度** | 毫秒级 | 秒级（慢1000-30000倍） |
| **API成本** | 免费 | 需要付费 |
| **网络依赖** | 无需网络 | 需要网络连接 |
| **结果一致性** | 100%一致 | 可能有差异 |
| **配置复杂度** | 简单（2个参数） | 复杂（需要模型配置） |
| **命名规范** | 标准化命名 | 原始命名 |

## 🔧 核心功能

### 数据处理流程
1. **Excel文件读取** → 读取质控评分表Excel文件
2. **数据提取** → 提取6个核心列的医疗文档数据
3. **类别统计** → 统计4个特定类别的唯一值
4. **翻译处理** → 生成标准化英文翻译
5. **文件输出** → 生成JSON、Excel、Markdown格式文件

### 翻译覆盖范围
- **规则类型**：8种（内容完整性、数据一致性等）
- **分类**：3种（手术科室、非手术科室、病历首页）
- **所属项目**：15种（入院记录、出院记录等）
- **文书类型**：74种（详见category_translations.md）

## 📖 使用方法

### 命令行使用

#### 更新版本（推荐）⭐
```bash
python medical_record_list_generator_updated.py
```

#### 原始版本
```bash
python medical_record_list_generator.py
```

### 编程接口使用

```python
from medical_record_list_generator_updated import MedicalRecordListGenerator

# 创建生成器实例
generator = MedicalRecordListGenerator()

# 处理Excel文件
success = generator.process_excel_file("excel_file.xlsx", "./output")

if success:
    summary = generator.get_processing_summary()
    print(f"处理完成！总记录数: {summary['total_records']}")
```

### 测试运行

```bash
cd tests
python test_basic.py          # 基础功能测试
python demo_complete.py       # 完整功能演示
python test_optimized.py      # 优化功能测试
```

## 📄 输出文件

### JSON文件 (`Medical_Record_List_Json/` 目录)
按文书类型分类的详细记录，包含元数据、翻译映射和医疗记录数据。

**文件示例**：`AdmissionRecord.json`
```json
{
  "metadata": {
    "document_type_chinese": "入院记录",
    "document_type_english": "AdmissionRecord",
    "total_records": 25,
    "generated_time": "2025-08-07T10:30:00"
  },
  "translations": { ... },
  "records": [ ... ]
}
```

### Excel统计表 (输出目录)
**文件名**：`category_translations_YYYYMMDD_HHMMSS.xlsx`
- 汇总信息工作表：总体统计和类别概览
- 各类别工作表：中英文对照表，包含格式化样式

### Markdown统计表 (输出目录)
**文件名**：`category_translations_YYYYMMDD_HHMMSS.md`
- 统计摘要和类别统计表
- 详细的中英文翻译对照表

## 🔧 技术说明

### 依赖要求
```bash
pip install pandas>=1.3.0 openpyxl>=3.0.0
```

### Excel列映射处理
程序自动处理Excel文件中的列映射问题：
- 使用 **分类.1** 列作为"分类"字段数据源
- 自动映射6个核心列到标准字段名

### 数据字段映射
```
Excel列名 → JSON字段名
规则类型 → rule_type_chinese/rule_type_english
分类.1   → classification_chinese/classification_english
所属项目 → belonging_project_chinese/belonging_project_english
文书类型 → document_type_chinese/document_type_english
规则内容 → rule_content
扣分     → deduction_points
```

## 🔍 更新版本技术细节

### LLM依赖完全移除 ✅
更新版本已完全移除对大语言模型的依赖：
- ❌ 删除了 `generate_english_translations()` 方法
- ❌ 移除了 `llm_use` 函数调用
- ❌ 移除了模型配置参数
- ✅ 新增 `load_predefined_translations()` 方法
- ✅ 使用 `category_translations.md` 中的标准翻译映射

### 命名标准化改进 ✅
所有变量和函数名都遵循统一的命名标准：

**主要变更示例：**
- `category_stats` → `category_statistics`
- `output_dir` → `output_directory`
- `columns_mapping` → `column_mapping`
- `json_filepath` → `json_file_path`

### 预定义翻译映射
内置完整的翻译映射表，包含：
- **规则类型**：8个翻译（ContentCompleteness、DataConsistency等）
- **分类**：3个翻译（SurgeryDepartment、MedicalRecordFrontPage等）
- **所属项目**：15个翻译（AdmissionRecord、DischargeSummary等）
- **文书类型**：74个翻译（详见category_translations.md）

## ⚠️ 使用注意事项

### 文件路径
- 支持相对路径和绝对路径
- 程序具有智能路径检测功能
- 确保Excel文件存在且可读

### 输出目录
- 确保对输出目录有写入权限
- 生成的文件包含时间戳，避免覆盖
- JSON文件保存到 `Medical_Record_List_Json/` 目录

### Excel数据处理
- 自动使用"分类.1"列作为分类数据源
- 自动清理空行和无效数据
- 支持中文列名和特殊字符

## 🚀 性能优势

| 指标 | 更新版本 | 原始版本 | 提升倍数 |
|------|----------|----------|----------|
| 处理速度 | 毫秒级 | 秒级 | 1000-30000x |
| API成本 | ¥0 | 根据调用量 | 100% 节省 |
| 网络要求 | 无 | 必需 | - |
| 结果一致性 | 100% | 变化 | - |

## 📞 问题排查

### 常见问题
1. **文件找不到**：检查Excel文件路径是否正确
2. **权限错误**：确保对输出目录有写入权限
3. **数据为空**：检查Excel文件是否包含有效数据
4. **编码问题**：确保Excel文件使用UTF-8编码

### 日志查看
程序运行时会生成 `medical_record_generator.log` 文件，包含详细的执行信息和错误提示。

---

**🌟 推荐使用更新版本获得最佳体验！无需配置，开箱即用！**
