# -*- coding: utf-8 -*-
"""
测试翻译功能的超时处理
"""

import sys
import os
import time

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator
from config import deepseek_v3_config

def test_translation_with_timeout():
    """测试带超时处理的翻译功能"""
    print("=" * 80)
    print("测试翻译功能的超时处理和错误恢复")
    print("=" * 80)
    
    # 设置参数
    excel_path = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    model_config = deepseek_v3_config
    
    # 创建生成器实例
    generator = MedicalRecordListGenerator()
    
    # 执行前3步
    print("\n=== 执行基础步骤 ===")
    if not generator.read_excel_file(excel_path):
        print("❌ Excel文件读取失败")
        return False
    
    if not generator.validate_and_extract_data():
        print("❌ 数据验证和提取失败")
        return False
    
    if not generator.generate_category_statistics():
        print("❌ 类别统计生成失败")
        return False
    
    print("✅ 基础步骤完成")
    
    # 测试翻译功能（只翻译每个类别的前2个值）
    print("\n=== 测试翻译功能（带超时处理）===")
    
    limited_translations = {}
    total_success = 0
    total_timeout = 0
    total_error = 0
    
    for category, stats in generator.category_stats.items():
        # 只取前2个值进行测试
        test_values = stats['unique_values'][:2]
        print(f"\n🔄 测试翻译 {category} (前2个值):")
        
        category_translations = {}
        
        for i, chinese_term in enumerate(test_values, 1):
            print(f"  [{i}/{len(test_values)}] 翻译: {chinese_term}")
            
            try:
                # 记录开始时间
                start_time = time.time()
                
                # 创建翻译提示
                system_prompt = """你是一个专业的医疗术语翻译专家，专门负责将中文医疗术语准确翻译成英文。

翻译要求：
1. 使用标准的医疗英语术语和国际通用表达
2. 采用标题大小写格式（Title Case），如：Medical Record, Quality Control
3. 保持术语的专业性和准确性
4. 翻译应简洁明了，避免冗余词汇
5. 对于医疗文档类型，使用标准的医疗文档英文名称
6. 只返回英文翻译结果，不包含任何解释或其他内容

示例：
- 入院记录 → Admission Record
- 质控规则 → Quality Control Rule
- 病历管理 → Medical Record Management"""
                
                user_prompt = f"请将以下中文医疗术语翻译成英文，使用标题大小写格式，只返回英文翻译：{chinese_term}"
                
                # 使用线程实现超时控制
                import threading
                result = [None]
                exception = [None]
                
                def translation_thread():
                    try:
                        from model_use import llm_use
                        result[0] = llm_use(system_prompt, user_prompt, model_config)
                    except Exception as e:
                        exception[0] = e
                
                thread = threading.Thread(target=translation_thread)
                thread.daemon = True
                thread.start()
                thread.join(timeout=15)  # 15秒超时用于测试
                
                end_time = time.time()
                elapsed = end_time - start_time
                
                if thread.is_alive():
                    print(f"    ⏰ 超时 ({elapsed:.1f}s > 15s)")
                    category_translations[chinese_term] = chinese_term
                    total_timeout += 1
                elif exception[0]:
                    print(f"    ❌ 错误: {exception[0]}")
                    category_translations[chinese_term] = chinese_term
                    total_error += 1
                else:
                    english_translation = result[0]
                    if english_translation:
                        english_translation = english_translation.strip().strip('"').strip("'")
                        english_translation = generator._format_title_case(english_translation)
                        category_translations[chinese_term] = english_translation
                        print(f"    ✅ 成功 ({elapsed:.1f}s): {chinese_term} -> {english_translation}")
                        total_success += 1
                    else:
                        print(f"    ⚠️ 空结果 ({elapsed:.1f}s)")
                        category_translations[chinese_term] = chinese_term
                        total_error += 1
                
                # 添加延迟避免频率限制
                time.sleep(1)
                
            except Exception as e:
                print(f"    ❌ 异常: {e}")
                category_translations[chinese_term] = chinese_term
                total_error += 1
        
        limited_translations[category] = category_translations
    
    # 设置翻译结果
    generator.translations = limited_translations
    
    # 显示统计结果
    print("\n" + "=" * 60)
    print("翻译测试统计")
    print("=" * 60)
    total_attempts = total_success + total_timeout + total_error
    print(f"总尝试次数: {total_attempts}")
    print(f"成功翻译: {total_success} ({total_success/total_attempts*100:.1f}%)")
    print(f"超时次数: {total_timeout} ({total_timeout/total_attempts*100:.1f}%)")
    print(f"错误次数: {total_error} ({total_error/total_attempts*100:.1f}%)")
    
    # 测试文件生成
    print("\n=== 测试文件生成 ===")
    output_dir = "."
    
    if generator.generate_excel_statistics(output_dir):
        print("✅ Excel统计表生成成功")
    else:
        print("❌ Excel统计表生成失败")
    
    if generator.generate_markdown_statistics(output_dir):
        print("✅ Markdown统计表生成成功")
    else:
        print("❌ Markdown统计表生成失败")
    
    # 总结
    print("\n" + "=" * 80)
    print("🎉 超时处理测试完成！")
    print("=" * 80)
    
    if total_success > 0:
        print("✅ 翻译功能正常工作")
    if total_timeout > 0:
        print("⏰ 超时处理机制有效")
    if total_error > 0:
        print("🛡️ 错误处理机制有效")
    
    print("\n改进效果:")
    print("  - 添加了30秒超时限制，防止程序卡死")
    print("  - 使用线程实现非阻塞超时控制")
    print("  - 增加了详细的进度显示和时间统计")
    print("  - 添加了API调用间隔，避免频率限制")
    print("  - 完善了错误处理和日志记录")
    
    return True

if __name__ == "__main__":
    test_translation_with_timeout()
