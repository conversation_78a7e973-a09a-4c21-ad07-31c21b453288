# -*- coding: utf-8 -*-
"""
质控系统使用示例
演示如何使用增强的质控功能
"""
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))

from quality_control_main import QualityControlMain

def usage_example():
    """质控系统使用示例"""
    print("=" * 80)
    print("医疗文档质控系统使用示例")
    print("=" * 80)
    
    # 1. 初始化质控系统
    qc_main = QualityControlMain()
    
    # 2. 准备测试数据
    discharge_summary = {
        "patient_id": "P20240805001",
        "patient_name": "李明",
        "gender": "男",
        "age": 58,
        "content": """
        患者李明，男，58岁，因"胸痛伴气促5天"于2024-08-01入院。
        
        入院诊断：
        1. 冠心病 急性心肌梗死
        2. 高血压病3级 极高危
        
        入院情况：
        患者5天前无明显诱因出现胸骨后疼痛，呈压榨性，持续性，伴气促、大汗，
        休息后不能缓解，急诊就诊，心电图示急性前壁心肌梗死，急诊收入院。
        
        诊疗经过：
        入院后完善相关检查，冠脉造影示前降支近段完全闭塞，行急诊PCI术，
        术后给予双抗血小板、调脂稳斑、控制血压等治疗，病情逐渐稳定。
        
        出院诊断：
        1. 冠心病 急性ST段抬高型心肌梗死 前壁 PCI术后
        2. 高血压病3级 极高危
        
        出院情况：
        症状明显缓解，心功能基本正常，病情稳定，予以出院。
        
        出院医嘱：
        1. 阿司匹林肠溶片100mg qd po
        2. 氯吡格雷片75mg qd po
        3. 阿托伐他汀钙片40mg qn po
        4. 美托洛尔缓释片47.5mg qd po
        5. 定期复查心电图、心肌酶、血脂等
        6. 戒烟限酒，低盐低脂饮食
        7. 适当运动，避免剧烈活动
        8. 如有胸痛、气促等不适及时就诊
        """,
        "admission_date": "2024-08-01",
        "discharge_date": "2024-08-05",
        "diagnosis": {
            "admission": "冠心病 急性心肌梗死；高血压病3级 极高危",
            "discharge": "冠心病 急性ST段抬高型心肌梗死 前壁 PCI术后；高血压病3级 极高危"
        },
        "treatment": {
            "medications": ["阿司匹林", "氯吡格雷", "阿托伐他汀", "美托洛尔"],
            "procedures": ["冠脉造影", "PCI术"]
        }
    }
    
    initial_progress_note = {
        "patient_id": "P20240805002", 
        "patient_name": "王芳",
        "gender": "女",
        "age": 42,
        "content": """
        患者王芳，女，42岁，因"右下腹痛12小时"入院。
        
        病例特点：
        1. 中年女性，急性起病
        2. 右下腹痛12小时，呈持续性胀痛，伴恶心、呕吐
        3. 体温38.2℃，右下腹压痛、反跳痛阳性
        4. 血常规：WBC 15.2×10^9/L，N 85%
        5. 腹部CT：阑尾壁增厚，周围脂肪间隙模糊
        
        初步诊断：
        急性阑尾炎
        
        诊断依据：
        1. 典型的右下腹痛病史
        2. 右下腹压痛、反跳痛阳性
        3. 白细胞计数升高，中性粒细胞比例增高
        4. 腹部CT支持急性阑尾炎诊断
        
        鉴别诊断：
        1. 急性胆囊炎：疼痛部位不符
        2. 急性胃肠炎：缺乏典型体征
        3. 泌尿系结石：尿常规正常
        
        诊疗计划：
        1. 完善术前检查
        2. 禁食水，胃肠减压
        3. 抗感染治疗
        4. 择期行腹腔镜阑尾切除术
        5. 密切观察病情变化
        """,
        "admission_date": "2024-08-03",
        "record_time": "2024-08-03 14:30",
        "diagnosis": {
            "preliminary": "急性阑尾炎"
        }
    }
    
    print("\n📋 支持的功能:")
    qc_types = qc_main.get_supported_quality_control_types()
    for type_key, type_name in qc_types["types"].items():
        print(f"  • {type_name}")
    
    # 3. 演示不同类型的质控
    test_cases = [
        ("出院记录", discharge_summary, "出院小结质控"),
        ("首次病程记录", initial_progress_note, "初次病程记录质控")
    ]
    
    for doc_type, test_data, description in test_cases:
        print(f"\n" + "=" * 70)
        print(f"🏥 {description}")
        print("=" * 70)
        
        # 内涵质控示例
        print(f"\n🧠 1. 内涵质控 - {doc_type}")
        print("-" * 50)
        content_result = qc_main.run_content_quality_control(doc_type, test_data)
        if "error" in content_result:
            print(f"❌ 内涵质控失败: {content_result['error']}")
        else:
            metadata = content_result.get("metadata", {})
            print(f"✅ 内涵质控完成")
            print(f"   文档类型: {metadata.get('document_type_chinese')}")
            print(f"   质控规则: {metadata.get('total_rules', 0)}条")
            print(f"   执行时间: {metadata.get('timestamp', 'N/A')}")
        
        # 规则质控示例
        print(f"\n📋 2. 规则质控 - {doc_type}")
        print("-" * 50)
        rule_result = qc_main.run_rule_quality_control(doc_type, test_data)
        if "error" in rule_result:
            print(f"❌ 规则质控失败: {rule_result['error']}")
        else:
            metadata = rule_result.get("metadata", {})
            print(f"✅ 规则质控完成")
            print(f"   文档类型: {metadata.get('document_type_chinese')}")
            print(f"   质控规则: {metadata.get('total_rules', 0)}条")
            print(f"   执行时间: {metadata.get('timestamp', 'N/A')}")
        
        # 综合质控示例
        print(f"\n🔄 3. 综合质控 - {doc_type}")
        print("-" * 50)
        integrated_result = qc_main.run_integrated_quality_control(doc_type, test_data)
        if "error" in integrated_result:
            print(f"❌ 综合质控失败: {integrated_result['error']}")
        else:
            metadata = integrated_result.get("metadata", {})
            print(f"✅ 综合质控完成")
            print(f"   文档类型: {metadata.get('document_type_chinese')}")
            print(f"   总规则数: {metadata.get('total_rules', 0)}")
            print(f"   规则质控: {metadata.get('regulatory_count', 0)}条")
            print(f"   内涵质控: {metadata.get('connotation_count', 0)}条")
            
            # 生成质控摘要
            summary = qc_main.get_quality_control_summary(integrated_result)
            print(f"\n📊 质控摘要:")
            print(f"   综合得分: {summary.get('overall_score', 0):.1f}/100")
            print(f"   质量等级: {summary.get('quality_grade', '未知')}")
            print(f"   问题总数: {summary.get('problem_count', 0)}")
            
            reg_summary = summary.get("regulatory_summary", {})
            if reg_summary:
                print(f"   规则质控合规率: {reg_summary.get('compliance_rate', 0):.1f}%")
            
            con_summary = summary.get("connotation_summary", {})
            if con_summary:
                print(f"   内涵质控得分率: {con_summary.get('score_rate', 0):.1f}%")
    
    print(f"\n" + "=" * 80)
    print("🎉 质控系统演示完成")
    print("=" * 80)
    print("\n💡 使用提示:")
    print("1. 内涵质控：适用于评估医疗文档的专业性和完整性")
    print("2. 规则质控：适用于检查文档是否符合医疗规范")
    print("3. 综合质控：提供全面的质量评估和改进建议")
    print("4. 支持的文档类型：出院小结、初次病程记录")

if __name__ == "__main__":
    usage_example()
