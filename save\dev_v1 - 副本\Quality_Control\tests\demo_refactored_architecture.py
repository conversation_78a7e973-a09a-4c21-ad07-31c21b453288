# -*- coding: utf-8 -*-
"""
演示重构后的质控系统架构
展示三层架构设计和新功能
"""
import sys
from pathlib import Path

# 添加父目录路径以导入质控模块
sys.path.append(str(Path(__file__).parent.parent))

from quality_control_generator import QualityControlGenerator

def demo_file_name_flexibility():
    """演示灵活的文件名支持"""
    print("=" * 80)
    print("演示：灵活的文件名支持")
    print("=" * 80)
    
    generator = QualityControlGenerator()
    
    print("支持的输入格式示例：")
    test_cases = [
        ("discharge_summary.json", "完整文件名"),
        ("discharge_summary", "缺省扩展名"),
        ("discharge summary", "带空格的简化名"),
        ("Discharge Summary", "标题格式"),
        ("DISCHARGE SUMMARY", "全大写"),
        ("initial progress note", "首次病程记录简化名")
    ]
    
    for input_name, description in test_cases:
        normalized = generator.normalize_filename(input_name)
        file_path = generator.find_json_file(input_name)
        status = "✓ 找到" if file_path else "✗ 未找到"
        print(f"  {description:20} '{input_name}' -> '{normalized}' [{status}]")

def demo_architecture_design():
    """演示三层架构设计"""
    print("\n" + "=" * 80)
    print("演示：三层架构设计")
    print("=" * 80)
    
    print("重构后的架构层次：")
    print()
    print("1. 总控制层级 (quality_control_main.py)")
    print("   ├── 统一管理所有文档类型的质控流程")
    print("   ├── 提供统一的质控接口")
    print("   └── 负责结果汇总和报告生成")
    print()
    print("2. 子控制层级 ({document_type_english}_controller.py)")
    print("   ├── 负责特定文档类型的质控协调")
    print("   ├── 整合规则质控和内涵质控结果")
    print("   ├── 提供错误处理和异常管理")
    print("   └── 生成详细的质控摘要")
    print()
    print("3. 规则执行层级 ({rule_id}.py)")
    print("   ├── 每个规则一个独立的Python文件")
    print("   ├── 包含完整的检查逻辑和错误处理")
    print("   ├── 独立可测试和维护")
    print("   └── 统一的函数命名规范")

def demo_rule_id_naming():
    """演示基于rule_id的文件命名"""
    print("\n" + "=" * 80)
    print("演示：基于rule_id的文件命名")
    print("=" * 80)
    
    generator = QualityControlGenerator()
    
    try:
        # 加载出院记录数据
        json_files = generator.load_json_files("discharge summary")
        
        for doc_type, data in json_files.items():
            print(f"文档类型: {doc_type}")
            print(f"规则文件命名示例：")
            
            # 显示前10条规则的文件命名
            regulatory_rules = [r for r in data['records'] if r['type'] in ['规则', '规则和内涵']][:10]
            
            for i, rule in enumerate(regulatory_rules, 1):
                rule_id = rule['rule_id']
                rule_content = rule['rule_content'][:30] + "..." if len(rule['rule_content']) > 30 else rule['rule_content']
                print(f"  {i:2d}. {rule_id}.py  # {rule_content}")
                
            print(f"\n总共将生成 {len(regulatory_rules)} 个规则文件")
            
    except Exception as e:
        print(f"加载数据失败: {e}")

def demo_model_config_change():
    """演示模型配置更改"""
    print("\n" + "=" * 80)
    print("演示：模型配置更改")
    print("=" * 80)
    
    print("重构前后的模型配置对比：")
    print()
    print("重构前：")
    print("  ├── 规则质控代码生成: qwen_32B_config")
    print("  └── 内涵质控代码生成: glm_code_config")
    print()
    print("重构后：")
    print("  ├── 规则质控代码生成: glm_code_config  ← 已更改")
    print("  └── 内涵质控代码生成: glm_code_config")
    print()
    print("优势：")
    print("  ✓ 统一使用GLM-4.5-Flash模型")
    print("  ✓ 提高代码生成的一致性")
    print("  ✓ 降低模型配置管理复杂度")

def demo_generated_file_structure():
    """演示生成的文件结构"""
    print("\n" + "=" * 80)
    print("演示：生成的文件结构")
    print("=" * 80)
    
    print("重构后的文件组织结构：")
    print()
    print("Quality_Control/")
    print("├── quality_control_main.py                    # 总控制器")
    print("├── quality_control_generator.py               # 代码生成器（重构版）")
    print("├── Regulatory_Quality_Control/                # 规则质控目录")
    print("│   └── Discharge_Summary/                     # 出院记录规则质控")
    print("│       ├── discharge_summary_controller.py   # 子控制器")
    print("│       ├── rule_f071c274.py                  # 时效性规则")
    print("│       ├── rule_b24ccb01.py                  # 段落完整性规则")
    print("│       ├── rule_86604b78.py                  # 段落完整性规则")
    print("│       └── ...                               # 其他规则文件")
    print("└── Connotation_Quality_Control/               # 内涵质控目录")
    print("    └── Discharge_Summary/                     # 出院记录内涵质控")
    print("        ├── discharge_summary.py              # 主控制器")
    print("        ├── rule_xxx.py                       # 内涵质控处理")
    print("        ├── rule_xxx_prompt.json              # 提示词文件")
    print("        └── ...                               # 其他内涵质控文件")

def demo_command_line_usage():
    """演示命令行使用方法"""
    print("\n" + "=" * 80)
    print("演示：命令行使用方法")
    print("=" * 80)
    
    print("重构后的命令行接口：")
    print()
    print("1. 生成所有文档类型的质控代码：")
    print("   python quality_control_generator.py")
    print()
    print("2. 生成特定文档类型的质控代码：")
    print("   python quality_control_generator.py --file 'discharge summary'")
    print("   python quality_control_generator.py -f 'initial progress note'")
    print()
    print("3. 列出所有可用的JSON文件：")
    print("   python quality_control_generator.py --list")
    print("   python quality_control_generator.py -l")
    print()
    print("4. 支持的文件名格式：")
    print("   --file 'discharge_summary.json'")
    print("   --file 'discharge_summary'")
    print("   --file 'discharge summary'")
    print("   --file 'Discharge Summary'")

def main():
    """主演示函数"""
    print("重构后的质控系统架构演示")
    print("=" * 80)
    print("本演示展示了重构后的质控系统的新特性和改进")
    print("=" * 80)
    
    # 运行各项演示
    demo_file_name_flexibility()
    demo_architecture_design()
    demo_rule_id_naming()
    demo_model_config_change()
    demo_generated_file_structure()
    demo_command_line_usage()
    
    print("\n" + "=" * 80)
    print("重构总结")
    print("=" * 80)
    print("✓ 实现了三层架构设计")
    print("✓ 支持灵活的文件名输入格式")
    print("✓ 使用rule_id作为文件命名规范")
    print("✓ 统一使用glm_code_config进行代码生成")
    print("✓ 每个规则独立文件，便于测试和维护")
    print("✓ 完善的错误处理和异常管理")
    print("✓ 提供命令行接口和参数支持")
    print("=" * 80)

if __name__ == "__main__":
    main()
