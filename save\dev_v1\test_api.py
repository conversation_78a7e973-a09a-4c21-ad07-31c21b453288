# -*- coding: utf-8 -*-
"""
质控API测试脚本
测试Flask Web应用的质控接口功能
"""
import json
import requests
import time
from pathlib import Path

# API配置
API_BASE_URL = "http://localhost:9086"
API_ENDPOINTS = {
    "health": f"{API_BASE_URL}/hospical/health",
    "quality_control": f"{API_BASE_URL}/hospical/quality_control",
    "docs": f"{API_BASE_URL}/hospical/docs",
    "index": f"{API_BASE_URL}/hospical"
}

def test_api_health():
    """测试健康检查接口"""
    print("🏥 测试健康检查接口")
    print("-" * 40)
    
    try:
        response = requests.get(API_ENDPOINTS["health"], timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"服务状态: {data.get('status', '未知')}")
            components = data.get('components', {})
            for component, status in components.items():
                print(f"  {component}: {status}")
            print("✅ 健康检查通过")
        else:
            print(f"❌ 健康检查失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    return True

def test_api_docs():
    """测试API文档接口"""
    print("\n📖 测试API文档接口")
    print("-" * 40)
    
    try:
        response = requests.get(API_ENDPOINTS["docs"], timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"API标题: {data.get('title', '未知')}")
            print(f"API版本: {data.get('version', '未知')}")
            endpoints = data.get('endpoints', {})
            print(f"可用接口数: {len(endpoints)}")
            print("✅ API文档获取成功")
        else:
            print(f"❌ API文档获取失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")

def load_test_data():
    """加载测试数据"""
    test_data = {}
    
    # 加载出院小结测试数据
    discharge_file = Path(__file__).parent / "data" / "test_discharge_summary.json"
    if discharge_file.exists():
        with open(discharge_file, 'r', encoding='utf-8') as f:
            discharge_data = json.load(f)
            test_data["discharge"] = discharge_data['Data'][0]  # 使用第一条记录
    
    # 加载首次病程记录测试数据
    course_file = Path(__file__).parent / "data" / "test_first_course_record.json"
    if course_file.exists():
        with open(course_file, 'r', encoding='utf-8') as f:
            course_data = json.load(f)
            test_data["course"] = course_data['Data'][0]  # 使用第一条记录
    
    return test_data

def test_quality_control_api():
    """测试质控接口"""
    print("\n🔍 测试质控接口")
    print("-" * 40)
    
    # 加载测试数据
    test_data = load_test_data()
    
    if not test_data:
        print("❌ 无法加载测试数据")
        return
    
    # 测试出院小结质控
    if "discharge" in test_data:
        print("\n📄 测试出院小结质控:")
        test_single_quality_control("Discharge Summary", test_data["discharge"])
    
    # 测试首次病程记录质控
    if "course" in test_data:
        print("\n📄 测试首次病程记录质控:")
        test_single_quality_control("Initial Progress Note", test_data["course"])

def test_single_quality_control(doc_type, medical_data):
    """测试单个质控请求"""
    patient_name = medical_data.get('Patient', {}).get('PatientName', '未知')
    dept_name = medical_data.get('VisitInfo', {}).get('DeptName', '未知')
    
    print(f"  患者: {patient_name}")
    print(f"  科室: {dept_name}")
    print(f"  文档类型: {doc_type}")
    
    # 构建请求数据
    request_data = {
        "type": doc_type,
        "data": medical_data
    }
    
    try:
        # 发送请求
        print("  🔄 发送质控请求...")
        response = requests.post(
            API_ENDPOINTS["quality_control"],
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success', False):
                result = data.get('data', {})
                quality_score = result.get('quality_score', {})
                statistics = result.get('statistics', {})
                
                print(f"  ✅ 质控完成")
                print(f"  🏆 质控分数: {quality_score.get('total_score', 0)}/100")
                print(f"  📊 质控状态: {result.get('quality_status', '未知')}")
                print(f"  📈 质量等级: {statistics.get('quality_grade', '未评估')}")
                print(f"  ⚠️  问题总数: {statistics.get('total_problems', 0)}")
                
                # 显示主要问题
                issues = result.get('quality_issues', {})
                problem_count = 0
                for rule, issue in issues.items():
                    if issue != "无" and problem_count < 3:
                        print(f"    问题{problem_count + 1}: {rule} - {issue}")
                        problem_count += 1
                
            else:
                error = data.get('error', {})
                print(f"  ❌ 质控失败: {error.get('message', '未知错误')}")
        else:
            print(f"  ❌ 请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 连接失败: {e}")

def test_error_handling():
    """测试错误处理"""
    print("\n🚨 测试错误处理")
    print("-" * 40)
    
    # 测试无效的文档类型
    print("\n📝 测试无效文档类型:")
    invalid_type_data = {
        "type": "Invalid Type",
        "data": {}
    }
    
    try:
        response = requests.post(
            API_ENDPOINTS["quality_control"],
            json=invalid_type_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"  状态码: {response.status_code}")
        if response.status_code == 400:
            data = response.json()
            error = data.get('error', {})
            print(f"  ✅ 正确处理错误: {error.get('message', '未知错误')}")
        else:
            print(f"  ❌ 错误处理异常: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 连接失败: {e}")
    
    # 测试缺少必需参数
    print("\n📝 测试缺少必需参数:")
    missing_param_data = {
        "type": "Discharge Summary"
        # 缺少data参数
    }
    
    try:
        response = requests.post(
            API_ENDPOINTS["quality_control"],
            json=missing_param_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"  状态码: {response.status_code}")
        if response.status_code == 400:
            data = response.json()
            error = data.get('error', {})
            print(f"  ✅ 正确处理错误: {error.get('message', '未知错误')}")
        else:
            print(f"  ❌ 错误处理异常: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 连接失败: {e}")

def test_performance():
    """测试性能"""
    print("\n⚡ 测试API性能")
    print("-" * 40)
    
    # 加载测试数据
    test_data = load_test_data()
    
    if not test_data or "discharge" not in test_data:
        print("❌ 无法加载测试数据")
        return
    
    request_data = {
        "type": "Discharge Summary",
        "data": test_data["discharge"]
    }
    
    # 执行多次请求测试
    test_count = 3
    response_times = []
    
    print(f"📊 执行 {test_count} 次请求测试...")
    
    for i in range(test_count):
        try:
            start_time = time.time()
            response = requests.post(
                API_ENDPOINTS["quality_control"],
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            
            print(f"  请求 {i+1}: {response_time:.2f}秒 (状态码: {response.status_code})")
            
        except requests.exceptions.RequestException as e:
            print(f"  请求 {i+1}: 失败 - {e}")
    
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        print(f"\n📈 性能统计:")
        print(f"  平均响应时间: {avg_time:.2f}秒")
        print(f"  最快响应时间: {min_time:.2f}秒")
        print(f"  最慢响应时间: {max_time:.2f}秒")

def main():
    """主测试函数"""
    print("=" * 60)
    print("医疗质控API测试")
    print("=" * 60)
    print(f"API地址: {API_BASE_URL}")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待API服务启动...")
    time.sleep(2)
    
    # 执行测试
    if test_api_health():
        test_api_docs()
        test_quality_control_api()
        test_error_handling()
        test_performance()
    else:
        print("\n❌ API服务未启动，请先运行 python quality_control_api.py")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
