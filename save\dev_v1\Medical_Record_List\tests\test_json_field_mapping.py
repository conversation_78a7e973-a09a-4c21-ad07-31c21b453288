# -*- coding: utf-8 -*-
"""
测试JSON文件字段映射 - 验证所有字段使用正确的Excel列数据
"""

import sys
import os
import json
import pandas as pd

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator, _find_excel_file

def test_json_field_mapping():
    """测试JSON文件字段映射"""
    print("=" * 80)
    print("测试JSON文件字段映射 - 验证所有字段使用正确的Excel列数据")
    print("=" * 80)
    
    # 获取Excel文件路径
    excel_path = _find_excel_file()
    if not excel_path:
        print("❌ 无法找到Excel文件")
        return False
    
    print(f"📂 Excel文件路径: {excel_path}")
    
    # 直接读取Excel文件进行对比
    print("\n" + "=" * 60)
    print("Step 1: 直接读取Excel文件分析列数据")
    print("=" * 60)
    
    try:
        raw_excel_data = pd.read_excel(excel_path)
        print(f"✅ Excel文件读取成功，数据形状: {raw_excel_data.shape}")
        
        # 检查列名
        columns = list(raw_excel_data.columns)
        print(f"📋 Excel列名: {columns}")
        
        # 分析"分类"和"分类.1"列的数据
        if '分类' in columns and '分类.1' in columns:
            print(f"\n🔍 分析'分类'和'分类.1'列的数据:")
            
            # 获取唯一值
            classification_col1_values = set(raw_excel_data['分类'].dropna().unique())
            classification_col2_values = set(raw_excel_data['分类.1'].dropna().unique())
            
            print(f"   '分类'列唯一值: {sorted(list(classification_col1_values))}")
            print(f"   '分类.1'列唯一值: {sorted(list(classification_col2_values))}")
            
            # 查找24小时内入院死亡记录的数据
            death_records = raw_excel_data[raw_excel_data['文书类型'] == '24小时内入院死亡记录']
            if not death_records.empty:
                print(f"\n📊 '24小时内入院死亡记录'的Excel原始数据:")
                for idx, row in death_records.iterrows():
                    print(f"   记录 {idx}:")
                    print(f"     规则类型: {row.get('规则类型', 'N/A')}")
                    print(f"     分类(第1列): {row.get('分类', 'N/A')}")
                    print(f"     分类(第2列): {row.get('分类.1', 'N/A')}")
                    print(f"     所属项目: {row.get('所属项目', 'N/A')}")
                    print(f"     文书类型: {row.get('文书类型', 'N/A')}")
                    print(f"     规则内容: {str(row.get('规则内容', 'N/A'))[:50]}...")
        
    except Exception as e:
        print(f"❌ Excel文件读取失败: {e}")
        return False
    
    # 使用生成器读取数据
    print("\n" + "=" * 60)
    print("Step 2: 使用生成器读取和处理数据")
    print("=" * 60)
    
    generator = MedicalRecordListGenerator()
    
    if not generator.read_excel_file(excel_path):
        print("❌ 生成器读取Excel文件失败")
        return False
    
    if not generator.validate_and_extract_data():
        print("❌ 数据验证和提取失败")
        return False
    
    print("✅ 生成器数据处理成功")
    
    # 查找24小时内入院死亡记录的处理后数据
    death_processed_records = [record for record in generator.processed_data 
                              if record.get('文书类型') == '24小时内入院死亡记录']
    
    if death_processed_records:
        print(f"\n📊 '24小时内入院死亡记录'的处理后数据:")
        for i, record in enumerate(death_processed_records, 1):
            print(f"   记录 {i}:")
            print(f"     规则类型: {record.get('规则类型', 'N/A')}")
            print(f"     分类: {record.get('分类', 'N/A')}")
            print(f"     所属项目: {record.get('所属项目', 'N/A')}")
            print(f"     文书类型: {record.get('文书类型', 'N/A')}")
            print(f"     规则内容: {str(record.get('规则内容', 'N/A'))[:50]}...")
    
    # 检查JSON文件
    print("\n" + "=" * 60)
    print("Step 3: 检查生成的JSON文件")
    print("=" * 60)
    
    json_file_path = "../Medical_Record_List_Json/24-Hour_Admission_and_Death_Record.json"
    
    if os.path.exists(json_file_path):
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print(f"✅ JSON文件读取成功")
            
            # 检查翻译数据
            translations = json_data.get('translations', {})
            if '分类' in translations:
                print(f"\n📋 JSON中'分类'字段的翻译对照:")
                for chinese, english in translations['分类'].items():
                    print(f"   {chinese} -> {english}")
            
            # 检查记录数据
            records = json_data.get('records', [])
            if records:
                print(f"\n📊 JSON中'24小时内入院死亡记录'的记录数据:")
                for i, record in enumerate(records, 1):
                    print(f"   记录 {i}:")
                    print(f"     规则类型(中文): {record.get('rule_type_chinese', 'N/A')}")
                    print(f"     规则类型(英文): {record.get('rule_type_english', 'N/A')}")
                    print(f"     分类(中文): {record.get('classification_chinese', 'N/A')}")
                    print(f"     分类(英文): {record.get('classification_english', 'N/A')}")
                    print(f"     所属项目(中文): {record.get('belonging_project_chinese', 'N/A')}")
                    print(f"     文书类型(中文): {record.get('document_type_chinese', 'N/A')}")
                    print(f"     规则内容: {str(record.get('rule_content', 'N/A'))[:50]}...")
        
        except Exception as e:
            print(f"❌ JSON文件读取失败: {e}")
            return False
    else:
        print(f"⚠️ JSON文件不存在: {json_file_path}")
    
    # 数据一致性验证
    print("\n" + "=" * 60)
    print("Step 4: 数据一致性验证")
    print("=" * 60)
    
    # 验证分类字段是否使用了正确的列
    if death_processed_records and os.path.exists(json_file_path):
        print("🔍 验证分类字段数据来源:")
        
        # 从处理后数据获取分类值
        processed_classifications = [record.get('分类', '') for record in death_processed_records]
        
        # 从JSON文件获取分类值
        json_classifications = [record.get('classification_chinese', '') for record in records]
        
        print(f"   处理后数据中的分类值: {processed_classifications}")
        print(f"   JSON文件中的分类值: {json_classifications}")
        
        # 检查是否一致
        data_consistent = processed_classifications == json_classifications
        print(f"   数据一致性: {'✅ 一致' if data_consistent else '❌ 不一致'}")
        
        # 验证分类值是否来自"分类.1"列
        expected_values = ['手术科室', '非手术科室', '病历首页']
        actual_values = set(processed_classifications)
        
        print(f"   预期分类值(来自分类.1列): {expected_values}")
        print(f"   实际分类值: {list(actual_values)}")
        
        correct_source = actual_values.issubset(set(expected_values))
        print(f"   数据来源正确: {'✅ 正确' if correct_source else '❌ 错误'}")
        
        return data_consistent and correct_source
    
    return True

if __name__ == "__main__":
    success = test_json_field_mapping()
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if success:
        print("🎉 所有测试通过！")
        print("✅ JSON文件字段映射正确")
        print("✅ 分类字段使用了正确的Excel列数据")
        print("✅ 数据一致性验证通过")
    else:
        print("❌ 部分测试失败，需要进一步检查")
