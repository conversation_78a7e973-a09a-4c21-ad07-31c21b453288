# -*- coding: utf-8 -*-
"""
批量规则类型分类器
功能：批量处理Medical_Record_List_Json文件夹中的所有JSON文件，使用规则分类系统进行分类
"""

import os
import sys
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# 添加dev_v1/rule_type目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
rule_type_dir = os.path.join(current_dir, "dev_v1", "rule_type")
sys.path.insert(0, rule_type_dir)

# 导入规则分类相关模块
try:
    from rule_type_main import RuleTypeProcessor, get_model_config_by_name
    from rule_type_classifier import setup_logger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在正确的工作目录中运行此脚本")
    sys.exit(1)

# 设置日志
logger = setup_logger(__name__)


class BatchRuleTypeClassifier:
    """批量规则类型分类器"""
    
    def __init__(self, 
                 input_dir: str = "dev_v1/Medical_Record_List/Medical_Record_List_Json",
                 output_dir: str = "dev_v1/rule_type/rule_type_json",
                 model_config_name: str = "qwen_32B_config"):
        """
        初始化批量分类器
        
        Args:
            input_dir (str): 输入目录路径
            output_dir (str): 输出目录路径  
            model_config_name (str): 模型配置名称
        """
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.model_config_name = model_config_name
        
        # 获取模型配置
        self.model_config = get_model_config_by_name(model_config_name)
        if not self.model_config:
            raise ValueError(f"无法获取模型配置: {model_config_name}")
        
        # 创建处理器
        self.processor = RuleTypeProcessor(
            input_dir=input_dir,
            output_dir=output_dir,
            model_config=self.model_config
        )
        
        logger.info(f"批量分类器初始化完成")
        logger.info(f"输入目录: {os.path.abspath(self.input_dir)}")
        logger.info(f"输出目录: {os.path.abspath(self.output_dir)}")
        logger.info(f"使用模型: {self.model_config.get('model', 'Unknown')}")
    
    def get_json_files(self) -> List[str]:
        """
        获取输入目录中的所有JSON文件
        
        Returns:
            List[str]: JSON文件名列表
        """
        if not os.path.exists(self.input_dir):
            logger.error(f"输入目录不存在: {self.input_dir}")
            return []
        
        json_files = []
        try:
            for filename in os.listdir(self.input_dir):
                if filename.endswith('.json'):
                    json_files.append(filename)
            
            json_files.sort()  # 按文件名排序
            logger.info(f"找到 {len(json_files)} 个JSON文件")
            return json_files
            
        except Exception as e:
            logger.error(f"读取输入目录失败: {e}")
            return []
    
    def process_all_files(self) -> Dict[str, Any]:
        """
        批量处理所有JSON文件
        
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        json_files = self.get_json_files()
        if not json_files:
            logger.error("没有找到可处理的JSON文件")
            return {"success": False, "message": "没有找到可处理的JSON文件"}
        
        # 初始化统计信息
        stats = {
            "total_files": len(json_files),
            "processed_files": 0,
            "failed_files": 0,
            "total_rules": 0,
            "classification_stats": {"内涵": 0, "规则": 0, "规则和内涵": 0},
            "processing_time": 0,
            "failed_file_list": [],
            "processed_file_list": []
        }
        
        start_time = time.time()
        
        print(f"\n🚀 开始批量处理 {len(json_files)} 个JSON文件...")
        print(f"输入目录: {os.path.abspath(self.input_dir)}")
        print(f"输出目录: {os.path.abspath(self.output_dir)}")
        print(f"使用模型: {self.model_config.get('model', 'Unknown')}")
        print("=" * 80)
        
        # 逐个处理文件
        for i, filename in enumerate(json_files, 1):
            print(f"\n📄 处理文件 {i}/{len(json_files)}: {filename}")
            logger.info(f"开始处理文件 {i}/{len(json_files)}: {filename}")
            
            try:
                # 处理单个文件
                output_file = self.processor.process_file(filename)
                
                if output_file:
                    stats["processed_files"] += 1
                    stats["processed_file_list"].append(filename)
                    
                    # 读取处理结果统计信息
                    try:
                        with open(output_file, 'r', encoding='utf-8') as f:
                            result_data = json.load(f)
                        
                        metadata = result_data.get('metadata', {})
                        file_stats = metadata.get('classification_stats', {})
                        total_records = metadata.get('total_records', 0)
                        
                        stats["total_rules"] += total_records
                        
                        # 累加分类统计
                        for category, count in file_stats.items():
                            if category in stats["classification_stats"]:
                                stats["classification_stats"][category] += count
                            else:
                                stats["classification_stats"][category] = count
                        
                        print(f"✅ 处理成功: {total_records} 条规则")
                        print(f"   分类结果: {file_stats}")
                        
                    except Exception as e:
                        logger.warning(f"读取处理结果统计失败: {e}")
                        print(f"✅ 处理成功，但无法读取统计信息")
                
                else:
                    stats["failed_files"] += 1
                    stats["failed_file_list"].append(filename)
                    print(f"❌ 处理失败")
                    logger.error(f"文件处理失败: {filename}")
                
            except Exception as e:
                stats["failed_files"] += 1
                stats["failed_file_list"].append(filename)
                print(f"❌ 处理异常: {e}")
                logger.error(f"处理文件 {filename} 时发生异常: {e}")
            
            # 显示进度
            progress = (i / len(json_files)) * 100
            print(f"📊 进度: {progress:.1f}% ({i}/{len(json_files)})")
        
        # 计算总处理时间
        end_time = time.time()
        stats["processing_time"] = end_time - start_time
        
        # 保存批量处理统计报告
        self._save_batch_report(stats)
        
        return stats
    
    def _save_batch_report(self, stats: Dict[str, Any]) -> None:
        """
        保存批量处理统计报告
        
        Args:
            stats (Dict[str, Any]): 处理统计信息
        """
        try:
            report = {
                "batch_processing_report": {
                    "timestamp": datetime.now().isoformat(),
                    "model_config": self.model_config_name,
                    "model_name": self.model_config.get('model', 'Unknown'),
                    "input_directory": os.path.abspath(self.input_dir),
                    "output_directory": os.path.abspath(self.output_dir),
                    "statistics": stats
                }
            }
            
            report_file = os.path.join(self.output_dir, "batch_processing_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"批量处理报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"保存批量处理报告失败: {e}")


def main():
    """主函数"""
    try:
        print("=" * 80)
        print("🏥 医疗记录规则类型批量分类器")
        print("=" * 80)
        
        # 创建批量分类器
        classifier = BatchRuleTypeClassifier(
            input_dir="dev_v1/Medical_Record_List/Medical_Record_List_Json",
            output_dir="dev_v1/rule_type/rule_type_json", 
            model_config_name="qwen_32B_config"
        )
        
        # 执行批量处理
        stats = classifier.process_all_files()
        
        # 显示最终结果
        print("\n" + "=" * 80)
        print("📊 批量处理完成！")
        print("=" * 80)
        
        if stats.get("success", True):
            print(f"✅ 处理成功: {stats['processed_files']}/{stats['total_files']} 个文件")
            print(f"❌ 处理失败: {stats['failed_files']} 个文件")
            print(f"📝 总规则数: {stats['total_rules']} 条")
            print(f"⏱️  处理时间: {stats['processing_time']:.2f} 秒")
            
            # 显示分类统计
            classification_stats = stats['classification_stats']
            if classification_stats:
                print(f"\n📈 分类统计:")
                total_classified = sum(classification_stats.values())
                for category, count in classification_stats.items():
                    percentage = (count / total_classified * 100) if total_classified > 0 else 0
                    print(f"  {category}: {count} 条 ({percentage:.1f}%)")
            
            # 显示失败文件列表
            if stats['failed_file_list']:
                print(f"\n❌ 处理失败的文件:")
                for filename in stats['failed_file_list']:
                    print(f"  - {filename}")
            
            print(f"\n💾 输出目录: {os.path.abspath(classifier.output_dir)}")
            print(f"📋 处理报告: batch_processing_report.json")
            
        else:
            print(f"❌ 批量处理失败: {stats.get('message', '未知错误')}")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        logger.info("用户中断批量处理操作")
    except Exception as e:
        logger.error(f"批量处理程序执行出错: {e}")
        print(f"\n❌ 执行出错: {e}")
        print("💡 请查看日志文件获取详细错误信息")


if __name__ == "__main__":
    main()
