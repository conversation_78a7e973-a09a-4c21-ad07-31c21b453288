# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- 项目文档重组和标准化
- 目录级README文件完善
- 统一的项目结构说明

## [1.0.0] - 2025-08-04

### Added
- 医疗记录质量控制三元分类系统
- 混合型质控规则识别功能
- 条件性章节缺失分类修复
- 文件重组和目录结构优化

### Changed
- 规则分类从二元扩展为三元分类（规则/内涵/规则和内涵）
- 优化分类准确率达到100%
- 完善医疗文档标准配置管理

### Fixed
- 条件性章节缺失规则的错误分类问题
- 混合型质控规则的分类歧义
- 文件导入路径和依赖问题

## [0.9.0] - 2025-08-01

### Added
- 医疗记录列表生成器完整功能
- Excel转JSON转换器
- 多模型LLM配置支持
- 智能路径检测和文件处理

### Changed
- JSON文件输出路径优化到专门目录
- Excel和Markdown统计表保持在当前目录
- 翻译提示词优化，确保专业性和一致性

### Fixed
- 文件路径检测和智能查找功能
- Excel列映射处理问题
- 超时控制机制完善

## [0.8.0] - 2025-07-30

### Added
- 规则类型分类器核心功能
- 医疗文档标准配置系统
- LLM集成和多模型支持
- 批处理和命令行界面

### Changed
- 分类逻辑优化和精确度提升
- 系统架构模块化重构
- 用户界面中文化改进

### Fixed
- 规则分类准确性问题
- 文件处理异常和错误处理
- 日志系统和调试信息完善

## [0.7.0] - 2025-07-25

### Added
- Excel质控规则数据导入功能
- 基础的规则解析和处理逻辑
- 项目基础架构和配置系统
- 医疗质控规范文档整理

### Changed
- 项目结构初步建立
- 核心模块功能定义
- 技术栈和依赖确定

## [0.6.0] - 2025-07-20

### Added
- 项目初始化和基础框架
- 医疗病历质控智能代理系统概念设计
- 核心功能需求分析和规划
- 技术架构设计和模块划分

### Changed
- 确定项目技术路线和实现方案
- 建立开发环境和工具链
- 制定项目开发计划和里程碑

---

## 版本说明

### 版本号规则
本项目采用语义化版本号 (Semantic Versioning)：
- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 发布类型
- **🎉 Major Release**：重大功能更新或架构变更
- **✨ Minor Release**：新功能添加或重要改进
- **🐛 Patch Release**：问题修复和小幅优化
- **🚧 Development**：开发版本，功能不稳定

### 变更类型说明
- **Added**：新增功能
- **Changed**：现有功能的变更
- **Deprecated**：即将废弃的功能
- **Removed**：已移除的功能
- **Fixed**：问题修复
- **Security**：安全相关修复

---

## 重要里程碑

### 🎯 三元分类系统 (v1.0.0)
实现了医疗记录质量控制的三元分类功能，支持规则质控、内涵质控和混合质控三种类型，分类准确率达到100%。

### 📊 数据处理系统 (v0.9.0)
完成了完整的医疗记录数据处理流程，包括Excel解析、JSON转换、翻译服务和多格式输出。

### 🤖 智能分类器 (v0.8.0)
建立了基于LLM的智能规则分类系统，支持多种模型配置和批处理功能。

### 📋 规则导入系统 (v0.7.0)
实现了Excel质控规则的自动导入和解析功能，为后续智能处理奠定基础。

### 🏗️ 项目架构 (v0.6.0)
确立了项目的整体架构和技术方案，为系统开发提供了清晰的指导。

---

**维护说明**：本变更日志记录了项目的所有重要变更，包括新功能、改进、修复和架构调整。每个版本都经过充分测试，确保系统的稳定性和可靠性。
