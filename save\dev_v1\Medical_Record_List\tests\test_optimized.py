# -*- coding: utf-8 -*-
"""
测试优化后的医疗记录列表生成器
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator
from config import deepseek_v3_config

def test_optimized_functionality():
    """测试优化后的功能"""
    print("开始测试优化后的医疗记录列表生成器...")
    
    # 设置参数
    excel_path = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    model_config = deepseek_v3_config
    output_dir = "."  # 使用当前目录
    
    # 创建生成器实例
    generator = MedicalRecordListGenerator()
    
    # 执行前3步
    print("\n=== 执行基础步骤 ===")
    if not generator.read_excel_file(excel_path):
        return False
    if not generator.validate_and_extract_data():
        return False
    if not generator.generate_category_statistics():
        return False
    
    # 创建小规模翻译测试数据（只测试每个类别的前2个值）
    print("\n=== 创建小规模翻译测试 ===")
    limited_translations = {}
    
    for category, stats in generator.category_stats.items():
        # 只取前2个值进行翻译测试
        test_values = stats['unique_values'][:2]
        print(f"\n测试翻译 {category} 的前2个值: {test_values}")
        
        category_translations = {}
        for chinese_term in test_values:
            try:
                # 使用优化后的翻译提示
                system_prompt = """你是一个专业的医疗术语翻译专家，专门负责将中文医疗术语准确翻译成英文。

翻译要求：
1. 使用标准的医疗英语术语和国际通用表达
2. 采用标题大小写格式（Title Case），如：Medical Record, Quality Control
3. 保持术语的专业性和准确性
4. 翻译应简洁明了，避免冗余词汇
5. 对于医疗文档类型，使用标准的医疗文档英文名称
6. 只返回英文翻译结果，不包含任何解释或其他内容

示例：
- 入院记录 → Admission Record
- 质控规则 → Quality Control Rule
- 病历管理 → Medical Record Management"""
                
                user_prompt = f"请将以下中文医疗术语翻译成英文，使用标题大小写格式，只返回英文翻译：{chinese_term}"
                
                # 调用LLM进行翻译
                from model_use import llm_use
                english_translation = llm_use(system_prompt, user_prompt, model_config)
                
                if english_translation:
                    english_translation = english_translation.strip().strip('"').strip("'")
                    # 使用生成器的标题大小写格式化方法
                    english_translation = generator._format_title_case(english_translation)
                    category_translations[chinese_term] = english_translation
                    print(f"  {chinese_term} -> {english_translation}")
                else:
                    category_translations[chinese_term] = chinese_term
                    print(f"  {chinese_term} -> [翻译失败，使用原文]")
                    
            except Exception as e:
                print(f"  翻译 '{chinese_term}' 时出错: {e}")
                category_translations[chinese_term] = chinese_term
        
        limited_translations[category] = category_translations
    
    # 设置翻译结果
    generator.translations = limited_translations
    
    # 测试Excel和Markdown生成
    print("\n=== 测试Excel统计表生成 ===")
    if generator.generate_excel_statistics(output_dir):
        print("Excel统计表生成成功")
    else:
        print("Excel统计表生成失败")
    
    print("\n=== 测试Markdown统计表生成 ===")
    if generator.generate_markdown_statistics(output_dir):
        print("Markdown统计表生成成功")
    else:
        print("Markdown统计表生成失败")
    
    # 显示生成的文件
    print(f"\n=== 检查生成的文件 ===")
    current_files = [f for f in os.listdir(output_dir) if f.startswith('category_translations_')]
    if current_files:
        print(f"在当前目录中生成的文件:")
        for file in current_files:
            file_path = os.path.join(output_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size} bytes)")
    else:
        print("未找到生成的统计文件")
    
    print("\n优化功能测试完成！")
    return True

if __name__ == "__main__":
    test_optimized_functionality()
