# -*- coding: utf-8 -*-
"""
测试Excel列映射修改 - 验证"分类"字段使用"分类.1"列
"""

import sys
import os
import pandas as pd

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator

def test_excel_column_mapping():
    """测试Excel列映射修改"""
    print("=" * 80)
    print("测试Excel列映射修改 - 验证'分类'字段使用'分类.1'列")
    print("=" * 80)
    
    # 设置参数
    excel_path = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    
    print(f"📂 Excel文件路径: {excel_path}")
    print(f"🎯 测试目标: 验证'分类'字段使用Excel中的'分类.1'列")
    
    # 创建生成器实例
    generator = MedicalRecordListGenerator()
    
    # Step 1: 读取Excel文件
    print("\n" + "=" * 60)
    print("Step 1: 读取Excel文件并检查列结构")
    print("=" * 60)
    
    if not generator.read_excel_file(excel_path):
        print("❌ Excel文件读取失败")
        return False
    
    print(f"✅ Excel文件读取成功，数据形状: {generator.raw_data.shape}")
    
    # 检查列名
    columns = list(generator.raw_data.columns)
    print(f"\n📋 Excel文件中的所有列名:")
    for i, col in enumerate(columns, 1):
        print(f"   {i:2d}. {col}")
    
    # 检查是否存在重复的"分类"列
    classification_columns = [col for col in columns if '分类' in col]
    print(f"\n🔍 包含'分类'的列: {classification_columns}")
    
    if '分类' in columns and '分类.1' in columns:
        print("✅ 发现重复的分类列：'分类' 和 '分类.1'")
        
        # 比较两列的数据
        print("\n📊 比较'分类'和'分类.1'列的数据:")
        
        # 显示前10行的对比
        comparison_data = generator.raw_data[['分类', '分类.1']].head(10)
        print("前10行数据对比:")
        print(comparison_data.to_string())
        
        # 统计唯一值
        unique_col1 = set(generator.raw_data['分类'].dropna().unique())
        unique_col2 = set(generator.raw_data['分类.1'].dropna().unique())
        
        print(f"\n📈 统计信息:")
        print(f"   '分类'列唯一值数量: {len(unique_col1)}")
        print(f"   '分类.1'列唯一值数量: {len(unique_col2)}")
        print(f"   两列数据是否相同: {'是' if unique_col1 == unique_col2 else '否'}")
        
        if unique_col1 != unique_col2:
            print(f"   '分类'列独有值: {unique_col1 - unique_col2}")
            print(f"   '分类.1'列独有值: {unique_col2 - unique_col1}")
    
    # Step 2: 测试数据提取
    print("\n" + "=" * 60)
    print("Step 2: 测试修改后的数据提取逻辑")
    print("=" * 60)
    
    if not generator.validate_and_extract_data():
        print("❌ 数据验证和提取失败")
        return False
    
    print(f"✅ 数据提取成功，有效记录: {len(generator.processed_data)} 条")
    
    # 验证提取的数据
    if generator.processed_data:
        sample_record = generator.processed_data[0]
        print(f"\n📄 第一条记录示例:")
        for key, value in sample_record.items():
            print(f"   {key}: {value}")
        
        # 检查"分类"字段的数据
        classification_values = [record.get('分类', '') for record in generator.processed_data[:10]]
        print(f"\n🔍 前10条记录的'分类'字段值:")
        for i, value in enumerate(classification_values, 1):
            print(f"   {i:2d}. {value}")
    
    # Step 3: 生成类别统计
    print("\n" + "=" * 60)
    print("Step 3: 生成类别统计")
    print("=" * 60)
    
    if not generator.generate_category_statistics():
        print("❌ 类别统计生成失败")
        return False
    
    print("✅ 类别统计生成成功")
    
    # 显示"分类"类别的统计信息
    if '分类' in generator.category_stats:
        classification_stats = generator.category_stats['分类']
        print(f"\n📊 '分类'类别统计:")
        print(f"   唯一值数量: {classification_stats['count']}")
        print(f"   前10个值:")
        for i, value in enumerate(classification_stats['unique_values'][:10], 1):
            print(f"     {i:2d}. {value}")
        
        if len(classification_stats['unique_values']) > 10:
            print(f"     ... 还有 {len(classification_stats['unique_values']) - 10} 个")
    
    # Step 4: 验证特定记录
    print("\n" + "=" * 60)
    print("Step 4: 验证特定记录的数据来源")
    print("=" * 60)
    
    # 查找包含"24小时内入院死亡记录"的记录
    target_records = [record for record in generator.processed_data 
                     if '24小时内入院死亡记录' in str(record.get('文书类型', ''))]
    
    if target_records:
        print(f"✅ 找到 {len(target_records)} 条'24小时内入院死亡记录'相关记录")
        
        sample_record = target_records[0]
        print(f"\n📋 示例记录详情:")
        print(f"   文书类型: {sample_record.get('文书类型')}")
        print(f"   分类: {sample_record.get('分类')}")
        print(f"   规则类型: {sample_record.get('规则类型')}")
        print(f"   所属项目: {sample_record.get('所属项目')}")
        print(f"   规则内容: {sample_record.get('规则内容', '')[:50]}...")
        print(f"   扣分: {sample_record.get('扣分')}")
        
        # 验证这个"分类"值确实来自"分类.1"列
        print(f"\n🔍 验证数据来源:")
        print(f"   该记录的'分类'值: {sample_record.get('分类')}")
        print(f"   ✅ 此值应该来自Excel的'分类.1'列，而不是'分类'列")
    else:
        print("⚠️ 未找到'24小时内入院死亡记录'相关记录")
    
    # 总结
    print("\n" + "=" * 80)
    print("🎉 Excel列映射测试完成！")
    print("=" * 80)
    
    success_criteria = [
        '分类.1' in generator.raw_data.columns,
        len(generator.processed_data) > 0,
        '分类' in generator.category_stats,
        len(generator.category_stats['分类']['unique_values']) > 0
    ]
    
    all_success = all(success_criteria)
    
    print("📋 测试结果:")
    print(f"   ✅ Excel包含'分类.1'列: {'通过' if success_criteria[0] else '失败'}")
    print(f"   ✅ 数据提取成功: {'通过' if success_criteria[1] else '失败'}")
    print(f"   ✅ 分类统计生成: {'通过' if success_criteria[2] else '失败'}")
    print(f"   ✅ 分类数据有效: {'通过' if success_criteria[3] else '失败'}")
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if all_success else '❌ 部分失败'}")
    
    if all_success:
        print("\n🎉 列映射修改成功！")
        print("   - '分类'字段现在使用Excel中的'分类.1'列")
        print("   - 数据提取逻辑正常工作")
        print("   - 统计功能正常")
        print("   - 可以继续进行翻译和文件生成")
    
    return all_success

if __name__ == "__main__":
    test_excel_column_mapping()
