# -*- coding: utf-8 -*-
"""
质控代码生成器 - 重构版本
根据 JSON 文件生成规则质控和内涵质控代码
支持三层架构：主控制器 → 子控制器 → 规则文件
"""
import json
import os
import sys
import re
from pathlib import Path

# 添加父目录到路径以导入模块
sys.path.append(str(Path(__file__).parent.parent))
from model_use import llm_use
from config import (
    glm_code_config, qwen_32B_config, qwen_30B_config,
    kimi_k2_config, deepseek_r1_config, deepseek_v3_config, qwen3_code_config
)

class QualityControlGenerator:
    def __init__(self, json_dir="rule_type/rule_type_json"):
        self.json_dir = Path(__file__).parent.parent / json_dir
        self.base_dir = Path(__file__).parent

        # 可用的模型配置
        self.available_models = {
            'glm_code_config': {
                'config': glm_code_config,
                'name': 'GLM-4.5-Flash',
                'description': '推荐用于代码生成'
            },
            'qwen_32B_config': {
                'config': qwen_32B_config,
                'name': 'Qwen-32B',
                'description': '推荐用于内涵质控'
            },
            'qwen_30B_config': {
                'config': qwen_30B_config,
                'name': 'Qwen-30B',
                'description': '通用模型'
            },
            'kimi_k2_config': {
                'config': kimi_k2_config,
                'name': 'Kimi-K2',
                'description': '长文本处理'
            },
            'deepseek_r1_config': {
                'config': deepseek_r1_config,
                'name': 'DeepSeek-R1',
                'description': '推理能力强'
            },
            'deepseek_v3_config': {
                'config': deepseek_v3_config,
                'name': 'DeepSeek-V3',
                'description': '通用聊天模型'
            },
            'qwen3_code_config': {
                'config': qwen3_code_config,
                'name': 'Qwen3-Coder',
                'description': '专业代码模型'
            }
        }

    def normalize_filename(self, filename):
        """
        标准化文件名，支持多种输入格式

        Args:
            filename (str): 输入的文件名

        Returns:
            str: 标准化后的文件名（不含扩展名）
        """
        # 移除扩展名
        if filename.endswith('.json'):
            filename = filename[:-5]

        # 移除 _type 后缀（如果存在）
        if filename.endswith('_type'):
            filename = filename[:-5]

        # 处理空格和下划线
        filename = filename.replace(' ', '_')

        # 转换为标题格式（每个单词首字母大写）
        words = filename.split('_')
        normalized = '_'.join(word.capitalize() for word in words if word)

        return normalized

    def find_json_file(self, input_filename):
        """
        根据输入文件名查找对应的JSON文件

        Args:
            input_filename (str): 用户输入的文件名

        Returns:
            Path: JSON文件路径，如果未找到返回None
        """
        normalized = self.normalize_filename(input_filename)

        # 可能的文件名格式
        possible_names = [
            f"{normalized}_type.json",
            f"{normalized}.json",
            f"{normalized}_Type.json"
        ]

        for name in possible_names:
            file_path = self.json_dir / name
            if file_path.exists():
                return file_path

        # 如果直接匹配失败，尝试模糊匹配
        for json_file in self.json_dir.glob("*.json"):
            file_stem = json_file.stem.lower()
            input_lower = input_filename.lower().replace(' ', '_')

            if input_lower in file_stem or file_stem.replace('_type', '') == input_lower:
                return json_file

        return None

    def load_json_files(self, specific_file=None):
        """
        加载JSON文件

        Args:
            specific_file (str, optional): 特定文件名，如果提供则只加载该文件

        Returns:
            dict: 加载的JSON数据
        """
        json_files = {}

        if specific_file:
            # 加载特定文件
            file_path = self.find_json_file(specific_file)
            if not file_path:
                raise FileNotFoundError(f"未找到文件: {specific_file}")

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                document_type_english = data['metadata']['document_type_english']
                json_files[document_type_english] = data
                print(f"成功加载文件: {file_path}")
        else:
            # 加载所有文件
            for json_file in self.json_dir.glob("*.json"):
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    document_type_english = data['metadata']['document_type_english']
                    json_files[document_type_english] = data

        return json_files

    def get_available_documents(self):
        """
        获取所有可用的文档类型

        Returns:
            dict: 文档类型映射 {document_type_english: json_file_path}
        """
        documents = {}
        for json_file in self.json_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    document_type_english = data['metadata']['document_type_english']
                    documents[document_type_english] = json_file
            except (json.JSONDecodeError, KeyError) as e:
                print(f"警告: 无法解析文件 {json_file.name}: {e}")
                continue
        return documents

    def display_document_menu(self, documents):
        """
        显示文档类型选择菜单

        Args:
            documents (dict): 可用的文档类型
        """
        print("\n" + "=" * 60)
        print("📋 质控代码生成器 - 文档类型选择")
        print("=" * 60)
        print("请选择要生成质控代码的文档类型：")
        print()

        doc_list = list(documents.keys())
        for i, doc_type in enumerate(doc_list, 1):
            # 尝试获取中文名称（如果有的话）
            chinese_name = self._get_chinese_name(doc_type)
            if chinese_name:
                print(f"  {i}. {doc_type} ({chinese_name})")
            else:
                print(f"  {i}. {doc_type}")

        print()
        print("📝 输入选项：")
        print("  • 编号 (1-{})".format(len(doc_list)))
        print("  • 完整名称 (如: \"Discharge Summary\")")
        print("  • 简化名称 (如: \"discharge summary\")")
        print("  • list 或 l - 重新显示选项")
        print("  • quit 或 q - 退出程序")
        print("-" * 60)

    def _get_chinese_name(self, document_type_english):
        """
        根据英文文档类型获取中文名称

        Args:
            document_type_english (str): 英文文档类型

        Returns:
            str: 中文名称，如果没有则返回None
        """
        chinese_mapping = {
            "Discharge Summary": "出院记录",
            "Initial Progress Note": "首次病程记录",
            "Emergency Record": "急诊记录",
            "Surgery Record": "手术记录",
            "Consultation Record": "会诊记录",
            "Admission Record": "入院记录"
        }
        return chinese_mapping.get(document_type_english)

    def interactive_document_selection(self):
        """
        交互式文档类型选择

        Returns:
            str: 选择的文档类型，如果用户退出则返回None
        """
        documents = self.get_available_documents()

        if not documents:
            print("❌ 错误: 未找到任何可用的JSON配置文件")
            print(f"请检查目录: {self.json_dir}")
            return None

        doc_list = list(documents.keys())

        while True:
            self.display_document_menu(documents)

            try:
                user_input = input("👉 请输入您的选择: ").strip()

                # 处理特殊命令
                if user_input.lower() in ['quit', 'q']:
                    print("👋 退出程序")
                    return None

                if user_input.lower() in ['list', 'l']:
                    continue  # 重新显示菜单

                # 处理编号输入
                if user_input.isdigit():
                    index = int(user_input) - 1
                    if 0 <= index < len(doc_list):
                        selected_doc = doc_list[index]
                        print(f"✅ 已选择: {selected_doc}")
                        return selected_doc
                    else:
                        print(f"❌ 编号无效! 请输入 1-{len(doc_list)} 之间的数字")
                        continue

                # 处理文档类型名称输入
                # 首先尝试直接匹配
                if user_input in documents:
                    print(f"✅ 已选择: {user_input}")
                    return user_input

                # 尝试使用现有的文件查找逻辑
                json_file = self.find_json_file(user_input)
                if json_file:
                    # 从找到的文件中获取document_type_english
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        selected_doc = data['metadata']['document_type_english']
                        print(f"✅ 已选择: {selected_doc}")
                        return selected_doc

                # 如果都没找到，显示错误信息
                print("❌ 输入无效! 请输入：")
                print(f"  • 编号：1-{len(doc_list)}")
                print("  • 完整名称：如 \"Discharge Summary\"")
                print("  • 简化名称：如 \"discharge summary\"")
                print("  • 特殊命令：list, quit")
                print()

            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                return None
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                print("请重新输入")

    def display_model_menu(self):
        """显示模型配置选择菜单"""
        print("\n" + "=" * 60)
        print("🤖 模型配置选择")
        print("=" * 60)
        print("可用的模型配置：")
        print()

        model_list = list(self.available_models.keys())
        for i, model_key in enumerate(model_list, 1):
            model_info = self.available_models[model_key]
            print(f"  {i}. {model_info['name']} ({model_key})")
            print(f"     {model_info['description']}")
            print()

        print("📝 输入选项：")
        print(f"  • 编号 (1-{len(model_list)})")
        print("  • 配置名称 (如: \"glm_code_config\")")
        print("  • back 或 b - 返回上一步")
        print("  • quit 或 q - 退出程序")
        print("-" * 60)

    def interactive_model_selection(self):
        """
        交互式模型配置选择

        Returns:
            tuple: (regulatory_model_config, connotation_model_config) 或 (None, None) 如果用户退出
        """
        print("\n" + "=" * 60)
        print("🔧 模型配置设置")
        print("=" * 60)
        print("请选择模型配置方案：")
        print()
        print("  1. 使用默认配置 (推荐 - 速度快)")
        print("     • 规则质控: Qwen-32B (qwen_32B_config)")
        print("     • 内涵质控: Qwen-32B (qwen_32B_config)")
        print()
        print("  2. 使用高质量配置")
        print("     • 规则质控: GLM-4.5-Flash (glm_code_config)")
        print("     • 内涵质控: GLM-4.5-Flash (glm_code_config)")
        print()
        print("  3. 使用混合配置")
        print("     • 规则质控: GLM-4.5-Flash (glm_code_config)")
        print("     • 内涵质控: Qwen-32B (qwen_32B_config)")
        print()
        print("  4. 自定义配置")
        print("     • 分别选择规则质控和内涵质控的模型")
        print()
        print("📝 输入选项: 1-4 / quit 或 q")
        print("-" * 60)

        while True:
            try:
                user_input = input("👉 请选择配置方案: ").strip()

                if user_input.lower() in ['quit', 'q']:
                    print("👋 退出程序")
                    return None, None

                if user_input == '1':
                    print("✅ 已选择默认配置 (速度快)")
                    print("  • 规则质控: Qwen-32B")
                    print("  • 内涵质控: Qwen-32B")
                    return qwen_32B_config, qwen_32B_config

                elif user_input == '2':
                    print("✅ 已选择高质量配置")
                    print("  • 规则质控: GLM-4.5-Flash")
                    print("  • 内涵质控: GLM-4.5-Flash")
                    return glm_code_config, glm_code_config

                elif user_input == '3':
                    print("✅ 已选择混合配置")
                    print("  • 规则质控: GLM-4.5-Flash")
                    print("  • 内涵质控: Qwen-32B")
                    return glm_code_config, qwen_32B_config

                elif user_input == '4':
                    print("🔧 进入自定义配置模式")

                    # 选择规则质控模型
                    print("\n📋 选择规则质控模型:")
                    regulatory_model = self._select_single_model("规则质控")
                    if regulatory_model is None:
                        return None, None

                    # 选择内涵质控模型
                    print("\n🧠 选择内涵质控模型:")
                    connotation_model = self._select_single_model("内涵质控")
                    if connotation_model is None:
                        return None, None

                    return regulatory_model, connotation_model

                else:
                    print("❌ 输入无效! 请输入 1-4 或 quit")

            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                return None, None
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                print("请重新输入")

    def _select_single_model(self, model_type):
        """
        选择单个模型配置

        Args:
            model_type (str): 模型类型描述

        Returns:
            dict: 选择的模型配置，如果用户退出则返回None
        """
        model_list = list(self.available_models.keys())

        while True:
            self.display_model_menu()

            try:
                user_input = input(f"👉 请选择{model_type}模型: ").strip()

                if user_input.lower() in ['quit', 'q']:
                    print("👋 退出程序")
                    return None

                if user_input.lower() in ['back', 'b']:
                    return self.interactive_model_selection()

                # 处理编号输入
                if user_input.isdigit():
                    index = int(user_input) - 1
                    if 0 <= index < len(model_list):
                        selected_key = model_list[index]
                        selected_model = self.available_models[selected_key]
                        print(f"✅ {model_type}已选择: {selected_model['name']}")
                        return selected_model['config']
                    else:
                        print(f"❌ 编号无效! 请输入 1-{len(model_list)} 之间的数字")
                        continue

                # 处理配置名称输入
                if user_input in self.available_models:
                    selected_model = self.available_models[user_input]
                    print(f"✅ {model_type}已选择: {selected_model['name']}")
                    return selected_model['config']

                print("❌ 输入无效! 请输入：")
                print(f"  • 编号：1-{len(model_list)}")
                print("  • 配置名称：如 \"glm_code_config\"")
                print("  • 特殊命令：back, quit")
                print()

            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                return None
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                print("请重新输入")

    def _clean_generated_code(self, code: str) -> str:
        """
        清理生成的代码，移除Markdown标记和非代码文本

        Args:
            code (str): 原始生成的代码

        Returns:
            str: 清理后的代码
        """
        lines = code.split('\n')
        cleaned_lines = []
        in_docstring = False
        docstring_marker = None

        for line in lines:
            stripped_line = line.strip()

            # 移除Markdown代码块标记
            if stripped_line in ['```python', '```', '```py'] or stripped_line.startswith('```'):
                continue

            # 检查文档字符串状态
            if '"""' in line or "'''" in line:
                if not in_docstring:
                    in_docstring = True
                    docstring_marker = '"""' if '"""' in line else "'''"
                elif docstring_marker in line:
                    in_docstring = False
                    docstring_marker = None
                cleaned_lines.append(line)
                continue

            # 在文档字符串内部，保留所有内容
            if in_docstring:
                cleaned_lines.append(line)
                continue

            # 移除中文功能描述段落
            if self._is_description_text(stripped_line):
                continue

            # 保留其他所有行
            cleaned_lines.append(line)

        # 清理多余的空行
        result_lines = []
        prev_empty = False

        for line in cleaned_lines:
            is_empty = not line.strip()

            # 避免连续多个空行
            if is_empty and prev_empty:
                continue

            result_lines.append(line)
            prev_empty = is_empty

        # 移除开头和结尾的空行
        while result_lines and not result_lines[0].strip():
            result_lines.pop(0)
        while result_lines and not result_lines[-1].strip():
            result_lines.pop()

        return '\n'.join(result_lines)

    def _is_description_text(self, line: str) -> bool:
        """
        判断是否为描述性文字（非代码）

        Args:
            line (str): 要检查的行

        Returns:
            bool: 是否为描述性文字
        """
        if not line:
            return False

        # Python注释和文档字符串保留
        if line.startswith('#') or line.startswith('"""') or line.startswith("'''"):
            return False

        # 检查常见的中文描述模式
        description_patterns = [
            r'^这个代码实现了',
            r'^这个函数',
            r'^主要功能',
            r'^使用方法',
            r'^注意事项',
            r'^代码说明',
            r'^功能描述',
            r'^\d+\.\s*[^#]',  # 数字列表（非注释）
            r'^[一二三四五六七八九十]+[、.]',  # 中文数字列表
        ]

        for pattern in description_patterns:
            if re.match(pattern, line):
                return True

        # 检查是否包含大量中文且不是代码
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', line))
        total_chars = len(line)

        # 如果中文字符占比超过60%且不包含Python语法元素，可能是描述文字
        if total_chars > 15 and chinese_chars / total_chars > 0.6:
            # 但如果包含Python语法元素，则保留
            if not any(char in line for char in ['(', ')', '[', ']', '{', '}', '=', ':', 'import', 'def', 'class']):
                return True

        return False
    
    def generate_regulatory_quality_control(self, document_type_english, records, model_config=None):
        """
        生成规则质控代码 - 重构版本
        为每个规则生成独立的Python文件，使用rule_id作为文件名

        Args:
            document_type_english (str): 文档类型
            records (list): 规则记录列表
            model_config (dict, optional): 自定义模型配置，默认使用glm_code_config
        """
        # 将空格替换为下划线
        document_type_dir = document_type_english.replace(' ', '_')
        regulatory_dir = self.base_dir / "Regulatory_Quality_Control" / document_type_dir
        regulatory_dir.mkdir(parents=True, exist_ok=True)

        # 过滤规则质控记录
        regulatory_records = [r for r in records if r['type'] in ['规则', '规则和内涵']]

        generated_files = []

        for record in regulatory_records:
            # 使用rule_id作为文件名
            file_name = f"{record['rule_id']}.py"
            file_path = regulatory_dir / file_name

            # 使用大模型生成规则质控代码
            system_prompt = """你是一个专业的医疗质控代码生成专家。请根据提供的质控规则生成纯净的Python代码。

严格要求：
1. 只输出纯Python代码，不要包含任何Markdown标记（如```python或```）
2. 不要在代码中添加功能描述段落或中文说明文字
3. 只使用Python注释（#）和文档字符串（\"\"\"）来说明代码
4. 生成的函数应该接收病历数据作为输入
5. 返回二元判断结果（True表示有问题，False表示没有问题）
6. 函数名必须是：check_rule
7. 包含完整的错误处理逻辑
8. 代码应该是独立可测试的

输出格式：直接输出Python代码，不要任何额外的格式标记或说明文字。
"""

            user_prompt = f"""为以下质控规则生成Python代码：

规则ID：{record['rule_id']}
规则类型：{record['rule_type_chinese']} ({record['rule_type_english']})
规则内容：{record['rule_content']}
扣分：{record['deduction_points']}分

生成包含以下内容的Python文件：
1. 文件头注释（规则ID和描述）
2. 必要的导入语句
3. check_rule(medical_record)函数
4. 异常处理
5. 测试代码

直接输出Python代码，不要任何Markdown标记或额外说明。
"""

            # 使用指定的模型配置生成规则质控代码
            if model_config is None:
                model_config = qwen_32B_config
            generated_code = llm_use(system_prompt, user_prompt, model_config)

            if generated_code:
                # 清理生成的代码
                cleaned_code = self._clean_generated_code(generated_code)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_code)
                generated_files.append({
                    'file_name': file_name,
                    'rule_id': record['rule_id'],
                    'rule_content': record['rule_content'],
                    'rule_type': record['rule_type_chinese'],
                    'classification': record['classification_chinese'],
                    'deduction_points': record['deduction_points']
                })
                print(f"生成规则质控文件: {file_path}")

        # 生成子控制器文件
        self._generate_regulatory_sub_controller(document_type_english, document_type_dir, generated_files, regulatory_records)

        return generated_files
    
    def generate_connotation_quality_control(self, document_type_english, records, model_config=None):
        """
        生成内涵质控代码 - 重构版本
        生成子控制器和prompt配置文件，而不是单独的规则文件

        Args:
            document_type_english (str): 文档类型
            records (list): 规则记录列表
            model_config (dict, optional): 自定义模型配置，默认使用qwen_32B_config
        """
        # 将空格替换为下划线
        document_type_dir = document_type_english.replace(' ', '_')
        connotation_dir = self.base_dir / "Connotation_Quality_Control" / document_type_dir
        connotation_dir.mkdir(parents=True, exist_ok=True)

        # 过滤内涵质控记录
        connotation_records = [r for r in records if r['type'] in ['内涵', '规则和内涵']]

        if not connotation_records:
            print(f"未找到 {document_type_english} 的内涵质控规则")
            return []

        print(f"找到 {len(connotation_records)} 个内涵质控规则")

        # 生成prompt配置文件
        prompts_config = self._generate_connotation_prompts_config(
            document_type_english, document_type_dir, connotation_records, model_config
        )

        # 生成子控制器文件
        controller_file = self._generate_connotation_sub_controller(
            document_type_english, document_type_dir, connotation_records
        )

        generated_files = [
            {
                'type': 'prompts_config',
                'file_name': prompts_config,
                'rule_count': len(connotation_records)
            },
            {
                'type': 'sub_controller',
                'file_name': controller_file,
                'rule_count': len(connotation_records)
            }
        ]

        return generated_files

    def _generate_connotation_prompts_config(self, document_type_english, document_type_dir, connotation_records, model_config=None):
        """
        生成内涵质控prompt配置文件

        Args:
            document_type_english (str): 文档类型英文名
            document_type_dir (str): 文档类型目录名
            connotation_records (list): 内涵质控记录列表
            model_config (dict, optional): 模型配置

        Returns:
            str: 生成的配置文件名
        """
        # 生成配置文件名
        config_file_name = f"{document_type_dir.lower()}_prompts.json"
        connotation_dir = self.base_dir / "Connotation_Quality_Control" / document_type_dir
        connotation_dir.mkdir(parents=True, exist_ok=True)
        config_file_path = connotation_dir / config_file_name

        # 构建prompts配置
        prompts_config = {
            "document_type": document_type_english,
            "document_type_dir": document_type_dir,
            "total_rules": len(connotation_records),
            "generated_time": "2025-08-05",
            "model_config": "qwen_32B_config" if model_config is None else "custom",
            "prompts": {}
        }

        # 为每个内涵质控规则生成prompt配置
        for record in connotation_records:
            rule_id = record['rule_id']

            # 生成系统提示词
            system_prompt = f"""你是一个专业的医疗质控专家，专门负责{record.get('document_type_chinese', document_type_english)}的{record['rule_type_chinese']}质控。

你的任务是：
1. 仔细分析提供的病历内容
2. 根据质控标准进行评估
3. 提供详细的质控结果

质控标准：{record['rule_content']}
扣分标准：{record['deduction_points']}分

请按照以下格式输出结果：
1. 质控分数：具体得分（满分{record['deduction_points']}分）
2. 质控问题：是否存在问题，如果有请详细说明
3. 质控建议：基于内涵质控结果提出的改进建议

注意：请确保评估客观、准确，并提供具体的改进建议。"""

            # 生成用户提示词模板
            user_prompt = f"""请对以下{record.get('document_type_chinese', document_type_english)}进行{record['rule_type_chinese']}质控：

病历内容：
{{medical_record_content}}

患者信息：
{{patient_info}}

请严格按照质控标准进行评估，并提供详细的分析结果。"""

            # 添加到配置中
            prompts_config["prompts"][rule_id] = {
                "system_prompt": system_prompt,
                "user_prompt": user_prompt,
                "rule_info": {
                    "rule_type_chinese": record['rule_type_chinese'],
                    "rule_type_english": record['rule_type_english'],
                    "classification_chinese": record['classification_chinese'],
                    "classification_english": record['classification_english'],
                    "rule_content": record['rule_content'],
                    "deduction_points": record['deduction_points']
                }
            }

        # 保存配置文件
        with open(config_file_path, 'w', encoding='utf-8') as f:
            json.dump(prompts_config, f, ensure_ascii=False, indent=2)

        print(f"生成内涵质控prompt配置文件: {config_file_path}")
        return config_file_name

    def _generate_connotation_sub_controller(self, document_type_english, document_type_dir, connotation_records):
        """
        生成内涵质控子控制器文件

        Args:
            document_type_english (str): 文档类型英文名
            document_type_dir (str): 文档类型目录名
            connotation_records (list): 内涵质控记录列表

        Returns:
            str: 生成的控制器文件名
        """
        # 生成控制器文件名
        controller_file_name = f"{document_type_dir.lower()}_controller.py"
        connotation_dir = self.base_dir / "Connotation_Quality_Control" / document_type_dir
        connotation_dir.mkdir(parents=True, exist_ok=True)
        controller_file_path = connotation_dir / controller_file_name

        # 生成控制器代码
        controller_code = f'''# -*- coding: utf-8 -*-
"""
{document_type_english} 内涵质控子控制器
负责加载和管理该文档类型下所有内涵质控规则的prompt配置
"""
import json
import sys
from pathlib import Path

# 添加父目录到路径以导入模块
sys.path.append(str(Path(__file__).parent.parent.parent))
from model_use import llm_use
from config import qwen_32B_config

class {document_type_dir.replace('_', '')}ConnotationController:
    """
    {document_type_english} 内涵质控控制器
    """

    def __init__(self):
        self.document_type = "{document_type_english}"
        self.document_type_dir = "{document_type_dir}"
        self.base_dir = Path(__file__).parent
        self.prompts_config_file = self.base_dir / "{document_type_dir.lower()}_prompts.json"
        self.prompts_config = self._load_prompts_config()
        self.total_rules = len(self.prompts_config.get("prompts", {{}}))

    def _load_prompts_config(self):
        """
        加载prompt配置文件

        Returns:
            dict: prompt配置数据
        """
        try:
            with open(self.prompts_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"警告: 未找到prompt配置文件 {{self.prompts_config_file}}")
            return {{"document_type": self.document_type, "prompts": {{}}}}
        except json.JSONDecodeError as e:
            print(f"错误: 解析prompt配置文件失败: {{e}}")
            return {{"document_type": self.document_type, "prompts": {{}}}}

    def run_connotation_quality_control(self, medical_record):
        """
        运行所有内涵质控检查

        Args:
            medical_record (dict): 病历数据

        Returns:
            dict: 内涵质控结果
        """
        results = {{}}

        print(f"开始执行 {{self.document_type}} 内涵质控，共 {{self.total_rules}} 条规则...")

        for rule_id, prompt_config in self.prompts_config.get("prompts", {{}}).items():
            try:
                # 执行单个内涵质控规则
                result = self._execute_single_rule(rule_id, prompt_config, medical_record)

                rule_info = prompt_config.get("rule_info", {{}})
                rule_content = rule_info.get("rule_content", rule_id)

                results[rule_content] = {{
                    'rule_id': rule_id,
                    'rule_type': rule_info.get('rule_type_chinese', '未知'),
                    'classification': rule_info.get('classification_chinese', '未知'),
                    'deduction_points': rule_info.get('deduction_points', 0),
                    'score': result.get('score', 0),
                    'problems': result.get('problems', ''),
                    'suggestions': result.get('suggestions', ''),
                    'type': 'connotation'
                }}

                print(f"  ✓ 完成规则 {{rule_id}}")

            except Exception as e:
                print(f"  ✗ 规则 {{rule_id}} 执行失败: {{e}}")
                rule_info = prompt_config.get("rule_info", {{}})
                rule_content = rule_info.get("rule_content", rule_id)

                results[rule_content] = {{
                    'rule_id': rule_id,
                    'rule_type': rule_info.get('rule_type_chinese', '未知'),
                    'classification': rule_info.get('classification_chinese', '未知'),
                    'deduction_points': rule_info.get('deduction_points', 0),
                    'score': 0,
                    'problems': '执行失败',
                    'suggestions': '请检查规则配置',
                    'type': 'connotation',
                    'error': str(e)
                }}

        print(f"{{self.document_type}} 内涵质控执行完成，共检查 {{len(results)}} 条规则")
        return results

    def _execute_single_rule(self, rule_id, prompt_config, medical_record):
        """
        执行单个内涵质控规则

        Args:
            rule_id (str): 规则ID
            prompt_config (dict): prompt配置
            medical_record (dict): 病历数据

        Returns:
            dict: 质控结果
        """
        system_prompt = prompt_config.get("system_prompt", "")
        user_prompt_template = prompt_config.get("user_prompt", "")

        # 格式化用户提示词
        user_prompt = user_prompt_template.format(
            medical_record_content=medical_record.get("content", ""),
            patient_info=medical_record.get("patient_info", {{}})
        )

        # 调用大模型进行分析
        response = llm_use(system_prompt, user_prompt, qwen_32B_config)

        # 解析响应结果
        return self._parse_llm_response(response, prompt_config)

    def _parse_llm_response(self, response, prompt_config):
        """
        解析LLM响应结果

        Args:
            response (str): LLM响应
            prompt_config (dict): prompt配置

        Returns:
            dict: 解析后的结果
        """
        try:
            # 简单的文本解析逻辑
            # 这里可以根据实际需要实现更复杂的解析逻辑

            rule_info = prompt_config.get("rule_info", {{}})
            max_points = rule_info.get("deduction_points", 100)

            # 默认结果
            result = {{
                "score": max_points,  # 默认满分
                "problems": "",
                "suggestions": ""
            }}

            if response:
                # 尝试从响应中提取信息
                lines = response.strip().split('\\n')
                for line in lines:
                    line = line.strip()
                    if '质控分数' in line or '得分' in line:
                        # 尝试提取分数
                        import re
                        score_match = re.search(r'(\\d+)', line)
                        if score_match:
                            result["score"] = int(score_match.group(1))
                    elif '质控问题' in line or '问题' in line:
                        result["problems"] = line
                    elif '质控建议' in line or '建议' in line:
                        result["suggestions"] = line

            return result

        except Exception as e:
            print(f"解析LLM响应失败: {{e}}")
            rule_info = prompt_config.get("rule_info", {{}})
            return {{
                "score": 0,
                "problems": "解析失败",
                "suggestions": "请检查LLM响应格式",
                "error": str(e)
            }}

    def get_summary(self, results):
        """
        获取内涵质控结果摘要

        Args:
            results (dict): 质控结果

        Returns:
            dict: 摘要信息
        """
        total_rules = len(results)
        total_score = sum(r.get('score', 0) for r in results.values())
        max_possible_score = sum(r.get('deduction_points', 0) for r in results.values())

        return {{
            'document_type': self.document_type,
            'total_rules': total_rules,
            'total_score': total_score,
            'max_possible_score': max_possible_score,
            'score_rate': total_score / max_possible_score * 100 if max_possible_score > 0 else 0
        }}

# 兼容性函数，供主控制器调用
def run_connotation_quality_control(medical_record):
    """兼容性函数"""
    controller = {document_type_dir.replace('_', '')}ConnotationController()
    return controller.run_connotation_quality_control(medical_record)

if __name__ == "__main__":
    # 测试示例
    controller = {document_type_dir.replace('_', '')}ConnotationController()

    test_record = {{
        "content": "测试{document_type_english}内容",
        "patient_info": {{"name": "张三", "age": 45}}
    }}

    results = controller.run_connotation_quality_control(test_record)
    summary = controller.get_summary(results)

    print("\\n内涵质控结果摘要:")
    print(f"文档类型: {{summary['document_type']}}")
    print(f"总规则数: {{summary['total_rules']}}")
    print(f"总得分: {{summary['total_score']}}")
    print(f"最高分: {{summary['max_possible_score']}}")
    print(f"得分率: {{summary['score_rate']:.2f}}%")
'''

        # 保存控制器文件
        with open(controller_file_path, 'w', encoding='utf-8') as f:
            f.write(controller_code)

        print(f"生成内涵质控子控制器文件: {controller_file_path}")
        return controller_file_name

    def _generate_regulatory_sub_controller(self, document_type_english, document_type_dir, generated_files, records):
        """
        生成规则质控子控制器文件
        负责该文档类型下所有规则的协调和结果整合
        """
        controller_file_path = self.base_dir / "Regulatory_Quality_Control" / document_type_dir / f"{document_type_dir.lower()}_controller.py"

        imports = []
        function_calls = []

        for file_info in generated_files:
            module_name = file_info['file_name'].replace('.py', '')
            imports.append(f"from .{module_name} import check_rule")
            imports.append(f"from .{module_name} import check_rule as {file_info['rule_id']}_check")

        # 生成函数调用代码
        for file_info in generated_files:
            rule_id = file_info['rule_id']
            function_calls.append(f"        # 执行规则: {file_info['rule_content']}")
            function_calls.append(f"        try:")
            function_calls.append(f"            result = {rule_id}_check(medical_record)")
            function_calls.append(f"            results['{file_info['rule_content']}'] = {{")
            function_calls.append(f"                'rule_id': '{rule_id}',")
            function_calls.append(f"                'rule_type': '{file_info['rule_type']}',")
            function_calls.append(f"                'classification': '{file_info['classification']}',")
            function_calls.append(f"                'deduction_points': {file_info['deduction_points']},")
            function_calls.append(f"                'has_problem': result,")
            function_calls.append(f"                'type': 'regulatory'")
            function_calls.append(f"            }}")
            function_calls.append(f"        except Exception as e:")
            function_calls.append(f"            print(f'规则 {rule_id} 执行失败: {{e}}')")
            function_calls.append(f"            results['{file_info['rule_content']}'] = {{")
            function_calls.append(f"                'rule_id': '{rule_id}',")
            function_calls.append(f"                'rule_type': '{file_info['rule_type']}',")
            function_calls.append(f"                'classification': '{file_info['classification']}',")
            function_calls.append(f"                'deduction_points': {file_info['deduction_points']},")
            function_calls.append(f"                'has_problem': True,  # 执行失败视为有问题")
            function_calls.append(f"                'type': 'regulatory',")
            function_calls.append(f"                'error': str(e)")
            function_calls.append(f"            }}")
            function_calls.append("")

        controller_content = f'''# -*- coding: utf-8 -*-
"""
{document_type_english} 规则质控子控制器
负责协调该文档类型下的所有规则质控检查
"""
import sys
from pathlib import Path

# 导入所有规则检查函数
{chr(10).join(imports)}

class {document_type_english.replace(' ', '')}RegulatoryController:
    """
    {document_type_english} 规则质控控制器
    """

    def __init__(self):
        self.document_type = "{document_type_english}"
        self.total_rules = {len(generated_files)}

    def run_regulatory_quality_control(self, medical_record):
        """
        运行所有规则质控检查

        Args:
            medical_record (dict): 病历数据

        Returns:
            dict: 规则质控结果
        """
        results = {{}}

        print(f"开始执行 {{self.document_type}} 规则质控，共 {{self.total_rules}} 条规则...")

{chr(10).join(function_calls)}

        print(f"{{self.document_type}} 规则质控执行完成，共检查 {{len(results)}} 条规则")
        return results

    def get_summary(self, results):
        """
        获取质控结果摘要

        Args:
            results (dict): 质控结果

        Returns:
            dict: 摘要信息
        """
        total_rules = len(results)
        problem_rules = sum(1 for r in results.values() if r.get('has_problem', False))
        total_deduction = sum(r.get('deduction_points', 0) for r in results.values() if r.get('has_problem', False))

        return {{
            'document_type': self.document_type,
            'total_rules': total_rules,
            'problem_rules': problem_rules,
            'total_deduction_points': total_deduction,
            'compliance_rate': (total_rules - problem_rules) / total_rules * 100 if total_rules > 0 else 0
        }}

# 兼容性函数，供主控制器调用
def run_regulatory_quality_control(medical_record):
    """兼容性函数"""
    controller = {document_type_english.replace(' ', '')}RegulatoryController()
    return controller.run_regulatory_quality_control(medical_record)

if __name__ == "__main__":
    # 测试示例
    controller = {document_type_english.replace(' ', '')}RegulatoryController()

    test_record = {{
        "content": "测试{document_type_english}内容",
        "patient_info": {{}},
        "diagnosis": {{}},
        "treatment": {{}}
    }}

    results = controller.run_regulatory_quality_control(test_record)
    summary = controller.get_summary(results)

    print("\\n规则质控结果摘要:")
    print(f"文档类型: {{summary['document_type']}}")
    print(f"总规则数: {{summary['total_rules']}}")
    print(f"问题规则数: {{summary['problem_rules']}}")
    print(f"总扣分: {{summary['total_deduction_points']}}")
    print(f"合规率: {{summary['compliance_rate']:.2f}}%")
'''

        with open(controller_file_path, 'w', encoding='utf-8') as f:
            f.write(controller_content)

        print(f"生成规则质控子控制器: {controller_file_path}")

    def generate_all(self, specific_file=None, regulatory_model_config=None, connotation_model_config=None):
        """
        生成所有质控代码

        Args:
            specific_file (str, optional): 特定文件名，如果提供则只处理该文件
            regulatory_model_config (dict, optional): 规则质控模型配置
            connotation_model_config (dict, optional): 内涵质控模型配置
        """
        json_files = self.load_json_files(specific_file)

        for document_type_english, data in json_files.items():
            print(f"\n开始生成 {document_type_english} 的质控代码...")
            print(f"数据统计: 总记录数 {data['metadata']['total_records']}")

            if 'classification_stats' in data['metadata']:
                stats = data['metadata']['classification_stats']
                print(f"分类统计: 规则({stats.get('规则', 0)}) 内涵({stats.get('内涵', 0)}) 混合({stats.get('规则和内涵', 0)})")

            records = data['records']

            # 生成规则质控代码
            print(f"生成规则质控代码...")
            regulatory_files = self.generate_regulatory_quality_control(
                document_type_english, records, regulatory_model_config
            )
            print(f"生成了 {len(regulatory_files)} 个规则质控文件")

            # 生成内涵质控代码
            print(f"生成内涵质控代码...")
            connotation_files = self.generate_connotation_quality_control(
                document_type_english, records, connotation_model_config
            )
            print(f"生成了 {len(connotation_files)} 个内涵质控文件")

            print(f"{document_type_english} 质控代码生成完成!")
            print("-" * 60)

def main():
    """主函数，支持命令行参数和交互式模式"""
    import argparse

    parser = argparse.ArgumentParser(description='质控代码生成器 - 支持交互式模式')
    parser.add_argument(
        '--file', '-f',
        type=str,
        help='指定要处理的JSON文件名（支持多种格式）'
    )
    parser.add_argument(
        '--list', '-l',
        action='store_true',
        help='列出所有可用的JSON文件'
    )
    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='强制进入交互式模式'
    )

    args = parser.parse_args()

    generator = QualityControlGenerator()

    if args.list:
        print("可用的JSON文件:")
        for json_file in generator.json_dir.glob("*.json"):
            print(f"  - {json_file.name}")
        return

    # 交互式模式的条件：
    # 1. 用户明确指定 --interactive
    # 2. 没有提供 --file 参数（默认进入交互式模式）
    if args.interactive or not args.file:
        print("🚀 欢迎使用质控代码生成器交互式模式")
        print("=" * 60)

        # 交互式选择文档类型
        selected_document = generator.interactive_document_selection()
        if selected_document is None:
            return  # 用户退出

        # 交互式选择模型配置
        regulatory_model, connotation_model = generator.interactive_model_selection()
        if regulatory_model is None or connotation_model is None:
            return  # 用户退出

        # 显示配置摘要
        print("\n" + "=" * 60)
        print("📋 配置摘要")
        print("=" * 60)
        print(f"📄 文档类型: {selected_document}")

        # 获取模型名称用于显示
        regulatory_name = "未知模型"
        connotation_name = "未知模型"
        for key, model_info in generator.available_models.items():
            if model_info['config'] == regulatory_model:
                regulatory_name = model_info['name']
            if model_info['config'] == connotation_model:
                connotation_name = model_info['name']

        print(f"🤖 规则质控模型: {regulatory_name}")
        print(f"🧠 内涵质控模型: {connotation_name}")
        print("=" * 60)

        # 确认开始生成
        confirm = input("👉 确认开始生成质控代码？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("👋 已取消生成")
            return

        print("\n🔧 开始生成质控代码...")
        try:
            generator.generate_all(selected_document, regulatory_model, connotation_model)
            print("\n✅ 质控代码生成完成！")
        except Exception as e:
            print(f"\n❌ 生成过程中发生错误: {e}")
            print("请检查配置和网络连接")

    elif args.file:
        print(f"处理指定文件: {args.file}")
        try:
            generator.generate_all(args.file)
        except FileNotFoundError as e:
            print(f"错误: {e}")
            print("使用 --list 参数查看可用文件")

if __name__ == "__main__":
    main()
