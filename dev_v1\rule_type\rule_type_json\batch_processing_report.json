{"batch_processing_report": {"timestamp": "2025-08-07T14:53:38.040974", "model_config": "qwen_32B_config", "model_name": "qwen3-32b", "input_directory": "D:\\Documents\\Program\\CCOS\\LLMs2\\Hospital\\Medical_QA_Agent\\dev_v1\\Medical_Record_List\\Medical_Record_List_Json", "output_directory": "D:\\Documents\\Program\\CCOS\\LLMs2\\Hospital\\Medical_QA_Agent\\dev_v1\\rule_type\\rule_type_json", "statistics": {"total_files": 74, "processed_files": 74, "failed_files": 0, "total_rules": 820, "classification_stats": {"内涵": 395, "规则": 419, "规则和内涵": 6}, "processing_time": 0.650848388671875, "failed_file_list": [], "processed_file_list": ["24hourAdmissionandDeathRecord.json", "AdmissionDiagnosis.json", "AdmissionRecord.json", "AncillaryTests.json", "AnesthesiaInformedConsentForm.json", "AnesthesiaPostoperativeVisitRecord.json", "AnesthesiaPreoperativeVisitRecord.json", "AnesthesiaRecord.json", "AttendingPhysicianDailyRoundsRecord.json", "AttendingPhysicianInitialRoundingRecord.json", "BloodTransfusionRecord.json", "BloodTransfusionTherapyInformedConsentForm.json", "ChemotherapyInformedConsentForm.json", "ChiefPhysicianInitialRoundsRecord.json", "ComplexCaseChiefRoundsRecord.json", "ComplexCaseDiscussionRecord.json", "ConsultationProgressNote.json", "ConsultationRequestRecord.json", "ContactInformation.json", "CostInformation.json", "CriticalCaseAttendingPhysicianRoundingRecord.json", "CriticalIllnessNotification.json", "DailyProgressNote.json", "DeathCaseDiscussionRecord.json", "DeathRecord.json", "DeputyChiefPhysicianDailyRoundsRecord.json", "DiagnosticAgreement.json", "DischargeDiagnosisinjuryandPoisoning.json", "DischargeDiagnosisotherDiagnoses.json", "DischargeDiagnosispathologicalDiagnosis.json", "DischargeDiagnosisprimaryDiagnosis.json", "DischargeSummary.json", "ElectronicMedicalRecord.json", "HospitalizationStatus.json", "InformedConsentForm.json", "InformedConsentFormforAutomaticDischarge.json", "InformedConsentFormforOutofpocketItems.json", "InitialProgressNote.json", "InvasiveProcedureRecord.json", "MedicalOrder.json", "MedicalRecordQualityAssessment.json", "MedicalRecordQualityControl.json", "MedicalRecordWriting.json", "NewTechnologyInformedConsentForm.json", "Otherautopsy.json", "OtherbloodGroupTransfusionReaction.json", "OthercranialBrainInjuryComa.json", "OtherdischargeMethod.json", "OtherdrugAllergy.json", "OtherreadmissionPlan.json", "Otherrescue.json", "OutpatientandEmergencyDiagnosis.json", "PersonalInformation.json", "PostoperativeInitialProgressNote.json", "PostoperativeThreedayCourseRecord.json", "PowerofAttorney.json", "PregnantWomenandNewborns.json", "PreoperativeDiscussionRecord.json", "PreoperativeSummary.json", "RadiotherapyInformedConsentForm.json", "ResuscitationRecord.json", "ShiftHandoverRecord.json", "ShiftTakeoverRecord.json", "Signature.json", "SpecialExaminationspecialTreatmentConsentForm.json", "StageSummary.json", "SurgeryandProceduresprimarySurgeryandProcedures.json", "SurgicalConsentForm.json", "SurgicalRecord.json", "SurgicalSafetyChecklist.json", "SurgicalandProceduralotherSurgicalProcedures.json", "TransferInRecord.json", "TransferOutRecord.json", "Within24HoursAdmissionandDischargeRecord.json"]}}}