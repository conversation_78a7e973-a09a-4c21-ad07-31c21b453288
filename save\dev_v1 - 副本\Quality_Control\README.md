# 医疗病历质控系统 (Medical Quality Control System)

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-2.1-orange.svg)](CHANGELOG.md)

## 📋 系统概述

医疗病历质控系统是一个先进的病历质量控制平台，采用**三层架构设计**，结合了基于规则的自动化检查和基于大语言模型(LLM)的智能分析，实现对医疗病历的全面、精准质量评估。

### 🎯 核心特性

- **🏗️ 三层架构**：总控制器 → 子控制器 → 规则执行层
- **🤖 双重质控**：规则质控 + 内涵质控
- **🔧 自动化生成**：基于JSON配置自动生成质控代码
- **🧹 智能清理**：自动清理生成代码中的格式问题
- **📁 规范组织**：标准化的文件命名和目录结构
- **🧪 完整测试**：全面的测试套件和验证工具

## 🏗️ 系统架构

### 目录结构
```
Quality_Control/
├── quality_control_main.py              # 🎯 总控制器（第1层）
├── quality_control_generator.py         # 🔧 代码生成器
├── code_cleaner.py                      # 🧹 代码清理工具
├── docs/                                # 📚 详细文档
│   ├── README_REFACTOR.md              # 重构说明
│   ├── README_CODE_CLEANING.md         # 代码清理方案
│   ├── README_MODIFICATIONS.md         # 修改历史
│   └── README_SYSTEM.md                # 系统详细说明
├── tests/                               # 🧪 测试套件
│   ├── test_generator_refactor.py      # 生成器测试
│   ├── test_code_cleaner.py           # 清理工具测试
│   ├── test_modifications.py          # 修改验证测试
│   └── demo_refactored_architecture.py # 架构演示
├── Regulatory_Quality_Control/          # 📋 规则质控模块
│   ├── Discharge_Summary/              # 出院记录质控
│   │   ├── discharge_summary_controller.py  # 🎛️ 子控制器（第2层）
│   │   ├── rule_f071c274.py           # ⚙️ 规则文件（第3层）
│   │   ├── rule_b24ccb01.py           # ⚙️ 规则文件（第3层）
│   │   └── ...                        # 其他规则文件
│   └── Initial_Progress_Note/          # 首次病程记录质控
│       ├── initial_progress_note_controller.py
│       └── ...                        # 规则文件
└── Connotation_Quality_Control/        # 🧠 内涵质控模块
    ├── Discharge_Summary/
    │   ├── discharge_summary.py        # 内涵质控主控制器
    │   ├── rule_xxx.py                 # 内涵质控处理文件
    │   └── rule_xxx_prompt.json        # LLM提示词文件
    └── Initial_Progress_Note/
        └── ...                         # 内涵质控文件
```

### 三层架构设计

| 层级 | 组件 | 职责 | 示例文件 |
|------|------|------|----------|
| **第1层** | 总控制器 | 统一管理所有文档类型的质控流程 | `quality_control_main.py` |
| **第2层** | 子控制器 | 协调特定文档类型的质控检查 | `discharge_summary_controller.py` |
| **第3层** | 规则执行器 | 执行单一规则的具体检查逻辑 | `rule_f071c274.py` |

## 🚀 核心功能

### 1. 🧠 内涵质控 (Connotation Quality Control)
基于大语言模型(LLM)的智能质控，专注于需要医学专业判断的复杂质控项目：

- **🔍 诊断逻辑性校验**：评估诊断演进的合理性和一致性
- **📊 临床信息质量校验**：检查症状、检查结果、会诊记录的完整性和准确性
- **💊 治疗过程校验**：评估病程记录、治疗效果、并发症处理的合理性
- **🔗 跨要素一致性校验**：检查各部分内容的内在逻辑一致性

**技术特点**：
- 使用 `qwen_32B_config` 模型进行智能分析
- 基于专业医学提示词模板
- 返回结构化的质控分数、问题描述和改进建议

### 2. 📋 规则质控 (Regulatory Quality Control)
基于代码规则的自动化质控，专注于结构化、可编程的质控检查：

- **✅ 基础信息完整性校验**：检查必填字段的完整性
- **⏰ 时效性校验**：验证文档完成时间和时间逻辑
- **📝 格式规范性校验**：检查文档格式和结构规范
- **🏷️ 特殊标记校验**：验证肿瘤标记等特殊标识的一致性

**技术特点**：
- 使用 `glm_code_config` 模型生成规则代码
- 每个规则独立的Python文件，便于测试和维护
- 统一的函数命名规范：`check_rule(medical_record)`

## 🔧 快速开始

### 安装要求

- Python 3.8+
- 依赖包：`pathlib`, `json`, `re`
- LLM配置：需要配置 `glm_code_config` 和 `qwen_32B_config`

### 基本使用

#### 1. 生成质控代码
```bash
# 生成所有文档类型的质控代码
python quality_control_generator.py

# 生成特定文档类型的质控代码（支持多种格式）
python quality_control_generator.py --file "discharge summary"
python quality_control_generator.py --file "Discharge Summary"
python quality_control_generator.py --file "discharge_summary.json"

# 列出所有可用的JSON配置文件
python quality_control_generator.py --list
```

#### 2. 清理代码格式问题
```bash
# 清理所有质控系统生成的文件
python code_cleaner.py --all

# 清理特定文件
python code_cleaner.py --file path/to/file.py

# 清理目录中的所有Python文件
python code_cleaner.py --dir path/to/directory
```

#### 3. 运行质控检查
```python
from quality_control_main import QualityControlMain

# 创建质控主控制器
qc_main = QualityControlMain()

# 执行质控检查
medical_record = {
    "content": "病历内容...",
    "patient_info": {...},
    "diagnosis": {...}
}

results = qc_main.run_quality_control("Discharge_Summary", medical_record)
print(results)
```

## 📊 质控流程

### 整体流程图

```mermaid
graph TD
    A[输入病历数据] --> B[数据预处理]
    B --> C{质控类型选择}
    C -->|规则质控| D[Regulatory Quality Control]
    C -->|内涵质控| E[Connotation Quality Control]
    C -->|综合质控| F[并行执行两种质控]
    
    D --> G[基础信息检查]
    D --> H[时效性检查]
    D --> I[格式规范检查]
    D --> J[特殊标记检查]
    
    E --> K[诊断逻辑性检查]
    E --> L[临床信息质量检查]
    E --> M[治疗过程检查]
    E --> N[一致性检查]
    
    G --> O[规则质控结果]
    H --> O
    I --> O
    J --> O
    
    K --> P[内涵质控结果]
    L --> P
    M --> P
    N --> P
    
    F --> Q[结果合并]
    O --> Q
    P --> Q
    Q --> R[生成质控报告]
    R --> S[输出最终结果]
```

### 详细执行步骤

1. **数据输入与预处理**
   - 接收病历数据（JSON格式）
   - 验证数据结构完整性
   - 提取关键信息字段

2. **规则质控执行**
   - 基础信息完整性校验（15%权重）
   - 特殊标记校验（5%权重）
   - 时效性和格式检查

3. **内涵质控执行**
   - 诊断逻辑性校验（20%权重）
   - 临床信息质量校验（20%权重）
   - 治疗过程校验（20%权重）
   - 出院管理校验（15%权重）
   - 跨要素一致性校验（5%权重）

4. **结果整合与评分**
   - 计算各项目得分
   - 应用权重计算总分
   - 确定整体质控状态

5. **报告生成**
   - 生成详细质控报告
   - 提供改进建议
   - 输出标准化结果

## 评分体系

### 权重分配
| 质控项目 | 权重 | 质控类型 | 备注 |
|---------|------|----------|------|
| 基础信息完整性 | 15% | 规则质控 | 必填字段检查 |
| 诊断逻辑性 | 20% | 内涵质控 | 诊断演进合理性 |
| 临床信息质量 | 20% | 内涵质控 | 症状、检查、会诊记录 |
| 治疗过程 | 20% | 内涵质控 | 病程、疗效、并发症 |
| 出院管理 | 15% | 内涵质控 | 出院情况、用药建议 |
| 肿瘤标记一致性 | 5% | 规则质控 | 肿瘤标记与诊断一致性 |
| 跨要素一致性 | 5% | 内涵质控 | 各部分内在逻辑一致性 |

### 评分标准
- **90-100分**：质量优良，状态为"pass"
- **70-89分**：存在轻微问题，状态为"warning"
- **0-69分**：存在严重问题，状态为"error"

## 📚 API文档

### 主要类和方法

#### QualityControlMain
```python
class QualityControlMain:
    """质控系统主控制器"""

    def run_quality_control(self, document_type: str, medical_record: dict) -> dict:
        """
        执行质控检查

        Args:
            document_type (str): 文档类型（如 "Discharge_Summary"）
            medical_record (dict): 病历数据

        Returns:
            dict: 质控结果，包含规则质控和内涵质控结果
        """
```

#### QualityControlGenerator
```python
class QualityControlGenerator:
    """质控代码生成器"""

    def generate_all(self, specific_file: str = None) -> None:
        """生成质控代码"""

    def normalize_filename(self, filename: str) -> str:
        """标准化文件名，支持多种输入格式"""
```

#### CodeCleaner
```python
class CodeCleaner:
    """代码清理工具"""

    def clean_quality_control_files(self) -> None:
        """清理质控系统生成的所有文件"""
```

## 💻 使用方法

### 基本使用示例

```python
from quality_control_main import QualityControlMain

# 创建质控主控制器
qc_main = QualityControlMain()

# 准备病历数据
medical_record = {
    "content": "病历内容...",
    "patient_info": {
        "name": "张三",
        "age": 45,
        "gender": "男"
    },
    "diagnosis": {
        "primary": "高血压",
        "secondary": []
    }
}

# 执行质控检查
results = qc_main.run_quality_control("Discharge_Summary", medical_record)

# 规则质控
regulatory_result = regulatory_processor.check_quality(patient_info, medical_record)

# 合并结果
final_result = merge_quality_results(connotation_result, regulatory_result)
```

### 并发执行示例

```python
import asyncio
from Quality_Control import QualityControlSystem

# 创建质控系统
qc_system = QualityControlSystem()

# 并发执行质控
async def run_quality_control():
    result = await qc_system.check_quality_async(patient_info, medical_record)
    return result

# 执行
result = asyncio.run(run_quality_control())
```

## 输出格式

### 标准输出格式

```json
{
  "overall_score": 85.5,
  "overall_status": "warning",
  "total_deduction": 14.5,
  "timestamp": "2025-01-17T10:30:00Z",
  "quality_control_results": {
    "regulatory": {
      "score": 90.0,
      "status": "pass",
      "checks": [...]
    },
    "connotation": {
      "score": 82.0,
      "status": "warning",
      "checks": [...]
    }
  },
  "elements": [
    {
      "element": "基础信息完整性",
      "score": 100,
      "status": "pass",
      "weight": 0.15,
      "suggestions": []
    }
  ],
  "suggestions": [
    {
      "type": "问题类型代码",
      "message": "具体问题描述",
      "severity": "严重程度",
      "category": "问题分类",
      "elements": ["相关要素1", "相关要素2"]
    }
  ]
}
```

## 配置说明

### 模型配置
系统支持多种LLM模型配置，在`config.py`中定义：
- Qwen系列模型
- Kimi模型
- DeepSeek模型
- GLM模型

### 质控规则配置
- 规则质控：通过JSON文件定义规则
- 内涵质控：通过提示词模板配置LLM检查逻辑

## 测试说明

### 测试覆盖范围
- **单元测试**：各个模块的独立功能测试
- **集成测试**：模块间协作功能测试
- **性能测试**：并发处理能力测试
- **准确性测试**：质控结果准确性验证

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_connotation_quality_control.py
python -m pytest tests/test_regulatory_quality_control.py
python -m pytest tests/test_integration.py
```

## 扩展开发

### 添加新的质控规则
1. **规则质控**：在相应的JSON文件中添加新规则
2. **内涵质控**：在提示词模板中添加新的检查逻辑

### 自定义评分权重
修改权重配置，调整各质控项目的重要性

### 集成新的LLM模型
在`config.py`中添加新的模型配置

## 版本信息

- **当前版本**：1.0.0
- **最后更新**：2025-01-17
- **兼容性**：Python 3.8+

## 🔧 高级配置

### 模型配置详情
系统使用两种LLM配置：

- **`glm_code_config`**：用于生成规则质控代码
- **`qwen_32B_config`**：用于运行内涵质控分析

### JSON配置文件结构
质控规则配置位于 `../rule_type/rule_type_json/` 目录：

```json
{
  "metadata": {
    "document_type_english": "Discharge Summary",
    "total_records": 22
  },
  "records": [
    {
      "rule_id": "rule_f071c274",
      "rule_type_chinese": "时效性",
      "rule_content": "出院记录应在患者出院后24小时内完成",
      "type": "规则",
      "deduction_points": 5
    }
  ]
}
```

### 目录命名规范
- 文档类型目录使用下划线：`Discharge_Summary`
- 规则文件使用rule_id命名：`rule_f071c274.py`
- 子控制器使用标准命名：`discharge_summary_controller.py`

## 🧪 测试和验证

### 运行测试套件
```bash
# 运行所有测试
cd tests/
python test_modifications.py

# 运行特定测试
python test_generator_refactor.py
python test_code_cleaner.py
```

### 验证生成的代码
```bash
# 验证代码格式
python code_cleaner.py --all

# 测试代码生成
python quality_control_generator.py --file "discharge summary"
```

## 🔄 重构和修改历史

### v2.1 (2025-08-05) - 最新版本
- ✅ **测试文件重新组织**：移动到 `tests/` 目录
- ✅ **目录命名规范化**：使用下划线替代空格
- ✅ **代码清理功能**：自动清理生成代码的格式问题
- ✅ **三层架构实现**：完整的控制器层次结构

### v2.0 (2025-08-05) - 重构版本
- 🏗️ **架构重构**：实现三层架构设计
- 🔧 **代码生成器改进**：支持灵活的文件名输入
- 📁 **文件组织优化**：使用rule_id作为文件命名
- 🤖 **模型配置统一**：规则质控使用glm_code_config

### v1.0 - 初始版本
- 基础的规则质控和内涵质控功能
- 简单的文件组织结构

## 🛠️ 故障排除

### 常见问题

#### 1. 导入错误
```bash
ModuleNotFoundError: No module named 'quality_control_generator'
```
**解决方案**：确保在正确的目录下运行，或检查Python路径设置。

#### 2. 生成的代码包含格式问题
```bash
SyntaxError: invalid character '）' (U+FF09)
```
**解决方案**：运行代码清理工具：
```bash
python code_cleaner.py --all
```

#### 3. JSON文件未找到
```bash
FileNotFoundError: 未找到文件: discharge summary
```
**解决方案**：检查文件名格式，支持的格式包括：
- `discharge_summary.json`
- `discharge summary`
- `Discharge Summary`

### 调试技巧

1. **启用详细日志**：在代码中添加print语句查看执行流程
2. **检查生成的文件**：验证生成的规则文件是否正确
3. **使用测试工具**：运行测试套件验证功能

## 📖 详细文档

更多详细信息请参考 `docs/` 目录下的专门文档：

- [`docs/README_REFACTOR.md`](docs/README_REFACTOR.md) - 系统重构详细说明
- [`docs/README_CODE_CLEANING.md`](docs/README_CODE_CLEANING.md) - 代码清理解决方案
- [`docs/README_MODIFICATIONS.md`](docs/README_MODIFICATIONS.md) - 系统修改历史
- [`docs/README_SYSTEM.md`](docs/README_SYSTEM.md) - 系统架构详细说明

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系信息

- **项目维护者**：Medical QA Agent Team
- **版本**：2.1
- **最后更新**：2025-08-05
- **Python兼容性**：3.8+

---

**🎯 快速链接**
- [快速开始](#-快速开始) | [API文档](#-api文档) | [测试指南](#-测试和验证) | [故障排除](#️-故障排除)
