# 规则ID: rule_3fc8b2c8
# 描述: 检查出院前一天或当天是否有上级医生同意出院记录

import datetime

def check_rule(medical_record):
    """
    检查病历是否缺少上级医生同意出院记录
    返回True表示存在问题需要扣分，False表示符合要求
    """
    try:
        # 获取出院日期（假设存储为字符串格式：YYYY-MM-DD）
        discharge_date_str = medical_record.get('discharge_date')
        if not discharge_date_str:
            return True  # 缺失出院日期
            
        # 转换为日期对象
        discharge_date = datetime.datetime.strptime(discharge_date_str, '%Y-%m-%d').date()
        
        # 获取上级医生同意日期
        approval_date_str = medical_record.get('physician_approval_date')
        if not approval_date_str:
            return True  # 缺少上级医生同意日期
            
        approval_date = datetime.datetime.strptime(approval_date_str, '%Y-%m-%d').date()
        
        # 计算出院前一天
        one_day = datetime.timedelta(days=1)
        day_before_discharge = discharge_date - one_day
        
        # 检查是否在出院前一天或当天
        if approval_date not in [day_before_discharge, discharge_date]:
            return True
            
        return False
            
    except (KeyError, ValueError, TypeError) as e:
        # 处理日期格式错误或字段缺失
        print(f"数据格式错误: {e}")
        return True
    except Exception as e:
        # 捕获其他未知异常
        print(f"未知错误: {e}")
        return True

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 正常情况（同意日期在出院当天）
    test1 = {
        'discharge_date': '2023-05-10',
        'physician_approval_date': '2023-05-10'
    }
    assert check_rule(test1) == False
    
    # 测试用例2: 同意日期在出院前一天
    test2 = {
        'discharge_date': '2023-05-10',
        'physician_approval_date': '2023-05-09'
    }
    assert check_rule(test2) == False
    
    # 测试用例3: 缺少同意记录
    test3 = {
        'discharge_date': '2023-05-10',
        'physician_approval_date': None
    }
    assert check_rule(test3) == True
    
    # 测试用例4: 无效日期格式
    test4 = {
        'discharge_date': '2023-05-10',
        'physician_approval_date': 'invalid-date'
    }
    assert check_rule(test4) == True
    
    # 测试用例5: 同意日期在出院后
    test5 = {
        'discharge_date': '2023-05-10',
        'physician_approval_date': '2023-05-11'
    }
    assert check_rule(test5) == True
    
    print("所有测试用例通过")