# Git忽略文件

# 配置文件
config.py

# 数据文件夹（包含大文件）
data/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE配置文件
.idea/
.vscode/
*.swp
*.swo
*~

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 大型文档文件
*.pdf
*.xlsx
*.xls
*.docx

# 日志文件
*.log
logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 模型文件
*.model
*.pkl
*.h5

# 临时文件
*.tmp
*.temp
*.bak 