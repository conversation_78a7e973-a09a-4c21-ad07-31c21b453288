"""
规则ID: rule_427f6155
规则描述: 首次病程缺初步诊断
"""

def check_rule(medical_record):
    """
    检查首次病程记录是否缺少初步诊断
    返回True表示存在问题，False表示符合要求
    """
    try:
        # 检查医疗记录是否包含首次病程记录字段
        if not isinstance(medical_record, dict):
            return True
            
        initial_course = medical_record.get('initial_course', '')
        
        # 判断是否缺失初步诊断内容
        if not initial_course or '初步诊断' not in initial_course:
            return True
            
        return False
            
    except Exception as e:
        # 捕获所有异常并记录错误
        print(f"规则执行错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 测试用例1: 缺失initial_course字段
    test1 = {}
    print(f"测试用例1结果: {check_rule(test1)}")  # 预期输出: True
    
    # 测试用例2: initial_course为空字符串
    test2 = {'initial_course': ''}
    print(f"测试用例2结果: {check_rule(test2)}")  # 预期输出: True
    
    # 测试用例3: 包含初步诊断
    test3 = {'initial_course': '患者入院后完善检查，初步诊断为高血压病。'}
    print(f"测试用例3结果: {check_rule(test3)}")  # 预期输出: False
    
    # 测试用例4: 不包含初步诊断
    test4 = {'initial_course': '患者主诉头痛头晕，完善血常规检查。'}
    print(f"测试用例4结果: {check_rule(test4)}")  # 预期输出: True