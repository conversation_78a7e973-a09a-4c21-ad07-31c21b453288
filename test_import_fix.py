#!/usr/bin/env python3
"""
测试导入修复的简单脚本
"""

import os
import sys

# 添加项目路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print(f"当前目录: {current_dir}")
print(f"Python路径: {sys.path[:3]}")  # 只显示前3个路径

try:
    from dev_v1.Quality_Control.quality_controller import quality_control
    print("✅ 成功导入 quality_control 函数")
    print(f"函数类型: {type(quality_control)}")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    
    # 尝试其他导入方式
    try:
        # 添加 dev_v1 目录到路径
        dev_v1_path = os.path.join(current_dir, 'dev_v1')
        sys.path.insert(0, dev_v1_path)
        from Quality_Control.quality_controller import quality_control
        print("✅ 成功导入 quality_control 函数 (方式2)")
    except ImportError as e2:
        print(f"❌ 第二种方式也失败: {e2}")
        
        # 检查文件是否存在
        quality_controller_path = os.path.join(current_dir, 'dev_v1', 'Quality_Control', 'quality_controller.py')
        print(f"质控控制器文件是否存在: {os.path.exists(quality_controller_path)}")
        print(f"文件路径: {quality_controller_path}")
