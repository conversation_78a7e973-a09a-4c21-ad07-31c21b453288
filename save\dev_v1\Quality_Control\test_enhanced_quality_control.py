# -*- coding: utf-8 -*-
"""
增强质控系统测试脚本
测试新增的内涵质控和规则质控独立调用功能
"""
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))

from quality_control_main import QualityControlMain

def test_enhanced_quality_control():
    """测试增强的质控功能"""
    print("=" * 80)
    print("增强质控系统功能测试")
    print("=" * 80)
    
    # 初始化质控系统
    qc_main = QualityControlMain()
    
    # 测试数据
    test_record = {
        "patient_id": "TEST001",
        "patient_name": "测试患者",
        "content": """
        患者，男，65岁，因"胸痛伴气促3天"入院。
        入院诊断：1.冠心病 心绞痛 2.高血压病3级 极高危
        入院情况：患者3天前无明显诱因出现胸骨后疼痛，呈压榨性，伴气促、出汗
        诊疗经过：入院后完善相关检查，给予抗血小板聚集、调脂稳斑、控制血压等治疗
        出院诊断：1.冠心病 心绞痛（稳定型） 2.高血压病3级 极高危
        出院情况：症状明显缓解，病情稳定
        出院医嘱：阿司匹林肠溶片100mg qd po，阿托伐他汀钙片20mg qn po，定期复查
        """,
        "admission_date": "2024-08-01",
        "discharge_date": "2024-08-05",
        "diagnosis": {
            "admission": "冠心病 心绞痛；高血压病3级 极高危",
            "discharge": "冠心病 心绞痛（稳定型）；高血压病3级 极高危"
        }
    }
    
    # 测试支持的文档类型
    print("\n📋 1. 测试支持的文档类型")
    print("-" * 50)
    supported_types = qc_main.get_supported_document_types()
    print(f"支持的文档类型数量: {supported_types['total_count']}")
    for chinese, english in supported_types["mappings"].items():
        print(f"  • {chinese} → {english}")
    
    # 测试支持的质控类型
    print("\n🔍 2. 测试支持的质控类型")
    print("-" * 50)
    qc_types = qc_main.get_supported_quality_control_types()
    for type_key, type_name in qc_types["types"].items():
        description = qc_types["descriptions"][type_key]
        print(f"  • {type_name} ({type_key})")
        print(f"    描述: {description}")
    
    # 测试不同质控类型
    document_types = ["出院记录", "首次病程记录"]
    
    for doc_type in document_types:
        print(f"\n🏥 3. 测试文档类型: {doc_type}")
        print("=" * 60)
        
        # 测试内涵质控
        print(f"\n🧠 3.1 内涵质控测试 - {doc_type}")
        print("-" * 40)
        try:
            content_results = qc_main.run_content_quality_control(doc_type, test_record)
            if "error" in content_results:
                print(f"❌ 内涵质控失败: {content_results['error']}")
            else:
                metadata = content_results.get("metadata", {})
                connotation_results = content_results.get("connotation_results", {})
                print(f"✅ 内涵质控成功")
                print(f"   质控类型: {metadata.get('quality_control_type')}")
                print(f"   规则数量: {metadata.get('total_rules', 0)}")
                print(f"   执行时间: {metadata.get('timestamp', 'N/A')}")
                
                # 显示部分结果
                if connotation_results and isinstance(connotation_results, dict):
                    print("   质控结果预览:")
                    for i, (rule, result) in enumerate(list(connotation_results.items())[:2]):
                        if isinstance(result, dict):
                            print(f"     规则{i+1}: {rule[:50]}...")
                            print(f"     得分: {result.get('score', 0)}/{result.get('deduction_points', result.get('max_points', 0))}")
                        else:
                            print(f"     规则{i+1}: {rule[:50]}...")
                            print(f"     结果: {str(result)[:30]}...")
        except Exception as e:
            print(f"❌ 内涵质控异常: {str(e)}")
        
        # 测试规则质控
        print(f"\n📋 3.2 规则质控测试 - {doc_type}")
        print("-" * 40)
        try:
            rule_results = qc_main.run_rule_quality_control(doc_type, test_record)
            if "error" in rule_results:
                print(f"❌ 规则质控失败: {rule_results['error']}")
            else:
                metadata = rule_results.get("metadata", {})
                regulatory_results = rule_results.get("regulatory_results", {})
                print(f"✅ 规则质控成功")
                print(f"   质控类型: {metadata.get('quality_control_type')}")
                print(f"   规则数量: {metadata.get('total_rules', 0)}")
                print(f"   执行时间: {metadata.get('timestamp', 'N/A')}")
                
                # 统计问题
                if isinstance(regulatory_results, dict):
                    problem_count = sum(1 for result in regulatory_results.values()
                                      if isinstance(result, dict) and result.get('has_problem', False))
                    print(f"   发现问题: {problem_count}/{len(regulatory_results)}")

                    # 显示部分结果
                    if regulatory_results:
                        print("   质控结果预览:")
                        for i, (rule, result) in enumerate(list(regulatory_results.items())[:2]):
                            if isinstance(result, dict):
                                status = "❌ 有问题" if result.get('has_problem', False) else "✅ 正常"
                                print(f"     规则{i+1}: {rule[:50]}...")
                                print(f"     状态: {status}")
                                print(f"     扣分: {result.get('deduction_points', 0)}")
                            else:
                                print(f"     规则{i+1}: {rule[:50]}...")
                                print(f"     结果: {str(result)[:30]}...")
                else:
                    print(f"   规则质控结果格式异常: {type(regulatory_results)}")
        except Exception as e:
            print(f"❌ 规则质控异常: {str(e)}")
        
        # 测试综合质控
        print(f"\n🔄 3.3 综合质控测试 - {doc_type}")
        print("-" * 40)
        try:
            integrated_results = qc_main.run_integrated_quality_control(doc_type, test_record)
            if "error" in integrated_results:
                print(f"❌ 综合质控失败: {integrated_results['error']}")
            else:
                metadata = integrated_results.get("metadata", {})
                print(f"✅ 综合质控成功")
                print(f"   质控类型: {metadata.get('quality_control_type')}")
                print(f"   总规则数: {metadata.get('total_rules', 0)}")
                print(f"   规则质控: {metadata.get('regulatory_count', 0)}条")
                print(f"   内涵质控: {metadata.get('connotation_count', 0)}条")
                
                # 生成摘要
                summary = qc_main.get_quality_control_summary(integrated_results)
                print(f"   综合得分: {summary.get('overall_score', 0):.1f}/100")
                print(f"   质量等级: {summary.get('quality_grade', '未知')}")
                print(f"   问题总数: {summary.get('problem_count', 0)}")
                
        except Exception as e:
            print(f"❌ 综合质控异常: {str(e)}")
    
    print(f"\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)

if __name__ == "__main__":
    test_enhanced_quality_control()
