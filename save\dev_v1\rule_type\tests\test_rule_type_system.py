# -*- coding: utf-8 -*-
"""
规则类型分类系统测试脚本
功能：验证整个系统的各项功能是否正常工作
"""

import os
import sys
import json
import logging
from pathlib import Path

# 添加父目录到路径以导入核心模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from rule_type_main import RuleTypeProcessor, get_model_config_by_name
from rule_type_classifier import classify_rule_type, get_available_model_configs

def test_model_configs():
    """测试模型配置加载"""
    print("=" * 60)
    print("测试1: 模型配置加载")
    print("=" * 60)
    
    configs = get_available_model_configs()
    print(f"✅ 成功加载 {len(configs)} 个模型配置:")
    
    for i, (name, config) in enumerate(configs.items(), 1):
        model_name = config.get('model', 'Unknown')
        api_url = config.get('request_url', 'Unknown')
        print(f"  {i}. {name}")
        print(f"     模型: {model_name}")
        print(f"     API: {api_url}")
    
    # 测试获取特定配置
    test_config = get_model_config_by_name('qwen_32B_config')
    if test_config:
        print(f"✅ 成功获取默认配置: {test_config['model']}")
    else:
        print("❌ 获取默认配置失败")
        return False
    
    return True


def test_rule_classifier():
    """测试规则分类器"""
    print("\n" + "=" * 60)
    print("测试2: 规则分类器")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "时效性规则",
            "rule": {
                "rule_content": "入院记录未在24小时内完成",
                "rule_type_chinese": "时效性",
                "classification_chinese": "时效",
                "document_type_chinese": "入院记录"
            },
            "expected": "规则"
        },
        {
            "name": "内容完整性规则",
            "rule": {
                "rule_content": "缺主诉",
                "rule_type_chinese": "内容完整性",
                "classification_chinese": "内涵",
                "document_type_chinese": "入院记录"
            },
            "expected": "内涵"
        },
        {
            "name": "签名规则",
            "rule": {
                "rule_content": "科主任签名(三级医院，可由病区负责医师代签)",
                "rule_type_chinese": "段落完整性",
                "classification_chinese": "缺项",
                "document_type_chinese": "签名"
            },
            "expected": "规则"
        }
    ]
    
    success_count = 0
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        print(f"规则内容: {test_case['rule']['rule_content']}")
        
        try:
            result = classify_rule_type(test_case['rule'])
            expected = test_case['expected']
            
            if result == expected:
                print(f"✅ 分类正确: {result}")
                success_count += 1
            else:
                print(f"❌ 分类错误: 期望 {expected}, 实际 {result}")
        except Exception as e:
            print(f"❌ 分类失败: {e}")
    
    print(f"\n分类测试结果: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_file_processing():
    """测试文件处理功能"""
    print("\n" + "=" * 60)
    print("测试3: 文件处理功能")
    print("=" * 60)
    
    # 查找最小的JSON文件进行测试
    input_dir = "../Medical_Record_List/Medical_Record_List_Json"
    if not os.path.exists(input_dir):
        print(f"❌ 输入目录不存在: {input_dir}")
        return False
    
    # 查找JSON文件
    json_files = []
    for file in os.listdir(input_dir):
        if file.endswith('.json'):
            file_path = os.path.join(input_dir, file)
            file_size = os.path.getsize(file_path)
            json_files.append((file, file_size))
    
    if not json_files:
        print(f"❌ 在 {input_dir} 中未找到JSON文件")
        return False
    
    # 选择最小的文件进行测试
    json_files.sort(key=lambda x: x[1])
    test_file = json_files[0][0]
    test_file_name = os.path.splitext(test_file)[0]
    
    print(f"选择测试文件: {test_file} ({json_files[0][1]} bytes)")
    
    try:
        # 创建处理器
        model_config = get_model_config_by_name('qwen_32B_config')
        processor = RuleTypeProcessor(
            input_dir=input_dir,
            output_dir="rule_type_json",
            model_config=model_config
        )
        
        # 处理文件
        output_file = processor.process_file(test_file_name)
        
        if output_file and os.path.exists(output_file):
            print(f"✅ 文件处理成功: {output_file}")
            
            # 验证输出文件格式
            with open(output_file, 'r', encoding='utf-8') as f:
                result_data = json.load(f)
            
            # 检查必要字段
            required_fields = ['metadata', 'translations', 'records']
            for field in required_fields:
                if field not in result_data:
                    print(f"❌ 输出文件缺少字段: {field}")
                    return False
            
            # 检查元数据更新
            metadata = result_data['metadata']
            if 'processing_time' not in metadata:
                print("❌ 元数据缺少 processing_time 字段")
                return False
            
            if 'type_classification_added' not in metadata:
                print("❌ 元数据缺少 type_classification_added 字段")
                return False
            
            # 检查记录是否添加了type字段
            records = result_data['records']
            if not records:
                print("❌ 输出文件没有记录")
                return False
            
            for record in records:
                if 'type' not in record:
                    print("❌ 记录缺少 type 字段")
                    return False
                
                if record['type'] not in ['内涵', '规则', '规则和内涵']:
                    print(f"❌ 无效的type值: {record['type']}")
                    return False
            
            print(f"✅ 输出文件格式验证通过")
            print(f"✅ 处理了 {len(records)} 条规则")
            
            # 显示分类统计
            stats = metadata.get('classification_stats', {})
            if stats:
                print(f"✅ 分类统计: 内涵={stats.get('内涵', 0)}, 规则={stats.get('规则', 0)}, 规则和内涵={stats.get('规则和内涵', 0)}")
            
            return True
        else:
            print("❌ 文件处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 文件处理异常: {e}")
        return False


def test_path_resolution():
    """测试路径解析功能"""
    print("\n" + "=" * 60)
    print("测试4: 路径解析功能")
    print("=" * 60)
    
    try:
        model_config = get_model_config_by_name('qwen_32B_config')
        processor = RuleTypeProcessor(model_config=model_config)
        
        # 测试不同的文件名格式
        test_cases = [
            "Other_autopsy",           # 不带扩展名
            "Other_autopsy.json",      # 带扩展名
        ]
        
        for test_case in test_cases:
            print(f"\n测试路径解析: {test_case}")
            found_path = processor._find_input_file(test_case)
            
            if found_path:
                print(f"✅ 找到文件: {os.path.abspath(found_path)}")
            else:
                print(f"❌ 未找到文件: {test_case}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 路径解析测试异常: {e}")
        return False


def test_output_directory():
    """测试输出目录创建"""
    print("\n" + "=" * 60)
    print("测试5: 输出目录创建")
    print("=" * 60)
    
    test_output_dir = "test_output_dir"
    
    try:
        model_config = get_model_config_by_name('qwen_32B_config')
        processor = RuleTypeProcessor(
            output_dir=test_output_dir,
            model_config=model_config
        )
        
        if os.path.exists(test_output_dir):
            print(f"✅ 输出目录创建成功: {os.path.abspath(test_output_dir)}")
            
            # 清理测试目录
            os.rmdir(test_output_dir)
            print("✅ 测试目录清理完成")
            return True
        else:
            print("❌ 输出目录创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 输出目录测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 规则类型分类系统测试")
    print("=" * 80)
    
    # 设置日志级别为WARNING，减少测试时的日志输出
    logging.getLogger().setLevel(logging.WARNING)
    
    tests = [
        ("模型配置加载", test_model_configs),
        ("规则分类器", test_rule_classifier),
        ("文件处理功能", test_file_processing),
        ("路径解析功能", test_path_resolution),
        ("输出目录创建", test_output_directory),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} - 通过")
            else:
                print(f"\n❌ {test_name} - 失败")
        except Exception as e:
            print(f"\n❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 测试总结")
    print("=" * 80)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
