"""
规则ID: rule_deda3efa
规则描述: 首次病程未在患者入院后8小时内完成
"""

import datetime

def check_rule(medical_record):
    """
    检查首次病程是否在入院后8小时内完成
    返回True表示有问题（未在8小时内完成），False表示合规
    """
    try:
        admission_time_str = medical_record['admission_time']
        first_course_time_str = medical_record['first_course_time']
        
        admission_time = datetime.datetime.strptime(admission_time_str, '%Y-%m-%d %H:%M:%S')
        first_course_time = datetime.datetime.strptime(first_course_time_str, '%Y-%m-%d %H:%M:%S')
        
        if first_course_time < admission_time:
            return True
            
        delta = first_course_time - admission_time
        if delta > datetime.timedelta(hours=8):
            return True
        return False
            
    except (KeyError, ValueError) as e:
        # 缺失必要字段或时间格式错误视为未完成
        return True

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 正确在8小时内完成
    test_case_1 = {
        'admission_time': '2023-01-01 10:00:00',
        'first_course_time': '2023-01-01 17:00:00'
    }
    assert check_rule(test_case_1) == False
    
    # 测试用例2: 超过8小时
    test_case_2 = {
        'admission_time': '2023-01-01 10:00:00',
        'first_course_time': '2023-01-01 18:01:00'
    }
    assert check_rule(test_case_2) == True
    
    # 测试用例3: 首次病程早于入院时间
    test_case_3 = {
        'admission_time': '2023-01-01 10:00:00',
        'first_course_time': '2023-01-01 09:00:00'
    }
    assert check_rule(test_case_3) == True
    
    # 测试用例4: 缺失首次病程时间
    test_case_4 = {
        'admission_time': '2023-01-01 10:00:00'
    }
    assert check_rule(test_case_4) == True
    
    # 测试用例5: 时间格式错误
    test_case_5 = {
        'admission_time': 'invalid',
        'first_course_time': '2023-01-01 18:00:00'
    }
    assert check_rule(test_case_5) == True
    
    print("所有测试用例通过")