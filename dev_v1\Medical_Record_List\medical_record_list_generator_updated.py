# -*- coding: utf-8 -*-
"""
医疗记录列表生成器 (Medical Record List Generator)
功能：
1. 读取Excel文件中的质控评分表数据
2. 提取医疗文档相关数据的6个列
3. 统计4个特定类别的唯一值
4. 使用预定义翻译映射生成标准化英文翻译
5. 按文书类型生成JSON文件
"""

import pandas as pd
import json
import os
import sys
import logging
import uuid
from typing import Dict, List, Any, Tuple
from datetime import datetime
from collections import Counter
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill

# 注意：此版本不再依赖LLM，直接使用预定义翻译

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('medical_record_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MedicalRecordListGenerator:
    """医疗记录列表生成器类 (Medical Record List Generator Class)"""

    def __init__(self):
        self.raw_data = None
        self.processed_data = []
        self.category_statistics = {}
        self.translations = {}
        # 使用标准化的英文命名作为内部标识符，但保持中文列名用于Excel读取
        self.required_columns = [
            '规则类型',      # ContentCompleteness, DataConsistency, etc.
            '分类',          # SurgeryDepartment, MedicalRecordFrontPage, etc.
            '所属项目',      # AdmissionRecord, DischargeSummary, etc.
            '文书类型',      # 24hourAdmissionandDeathRecord, etc.
            '规则内容',      # Rule Content
            '扣分'           # Deduction Points
        ]
        self.translation_categories = [
            '规则类型',      # Rule Type Categories
            '分类',          # Classification Categories
            '所属项目',      # Belonging Project Categories
            '文书类型'       # Document Type Categories
        ]

    def read_excel_file(self, file_path: str) -> bool:
        """
        读取Excel文件 (Read Excel File)

        Args:
            file_path (str): Excel文件路径

        Returns:
            bool: 读取是否成功
        """
        try:
            logger.info(f"开始读取Excel文件: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return False

            # 读取Excel文件
            self.raw_data = pd.read_excel(file_path)

            logger.info(f"成功读取Excel文件")
            logger.info(f"数据形状: {self.raw_data.shape}")
            logger.info(f"原始列名: {list(self.raw_data.columns)}")

            return True

        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            return False

    def validate_and_extract_data(self) -> bool:
        """
        验证并提取所需的6个列数据 (Validate and Extract Required Data)

        Returns:
            bool: 验证和提取是否成功
        """
        try:
            if self.raw_data is None:
                logger.error("原始数据为空，请先读取Excel文件")
                return False

            # 处理重复的列名问题（分类.1）
            column_mapping = {}
            available_columns = list(self.raw_data.columns)

            logger.info(f"Excel文件中的所有列名: {available_columns}")

            # 特殊处理：将"分类"字段映射到Excel中的"分类.1"列
            for required_column in self.required_columns:
                if required_column == '分类':
                    # 使用"分类.1"列而不是"分类"列
                    if '分类.1' in available_columns:
                        column_mapping[required_column] = '分类.1'
                        logger.info(f"将'{required_column}'字段映射到Excel列'{column_mapping[required_column]}'")
                    else:
                        logger.error(f"缺少必需的列: 分类.1")
                        return False
                else:
                    if required_column in available_columns:
                        column_mapping[required_column] = required_column
                    else:
                        logger.error(f"缺少必需的列: {required_column}")
                        return False

            logger.info("所有必需的列都存在")
            logger.info(f"列映射: {column_mapping}")

            # 提取所需列的数据，使用映射后的列名
            extracted_data = pd.DataFrame()
            for logical_column, excel_column in column_mapping.items():
                extracted_data[logical_column] = self.raw_data[excel_column]

            # 清理数据：移除空行和无效数据
            extracted_data = extracted_data.dropna(subset=['文书类型', '规则内容'])

            # 转换为字典列表
            self.processed_data = extracted_data.to_dict('records')

            logger.info(f"成功提取 {len(self.processed_data)} 条有效记录")

            return True

        except Exception as e:
            logger.error(f"数据验证和提取失败: {e}")
            return False

    def generate_category_statistics(self) -> bool:
        """
        统计4个特定类别的唯一值 (Generate Category Statistics)

        Returns:
            bool: 统计是否成功
        """
        try:
            if not self.processed_data:
                logger.error("处理后的数据为空，请先提取数据")
                return False

            logger.info("开始生成类别统计")

            # 统计每个类别的唯一值
            for category in self.translation_categories:
                unique_values = set()
                for record in self.processed_data:
                    value = str(record.get(category, '')).strip()
                    if value and value != 'nan':
                        unique_values.add(value)

                self.category_statistics[category] = {
                    'unique_values': sorted(list(unique_values)),
                    'count': len(unique_values)
                }

                logger.info(f"{category}: {len(unique_values)} 个唯一值")

            # 记录详细统计信息
            for category, statistics in self.category_statistics.items():
                logger.info(f"\n{category} 的唯一值:")
                for value in statistics['unique_values'][:10]:  # 只显示前10个
                    logger.info(f"  - {value}")
                if len(statistics['unique_values']) > 10:
                    logger.info(f"  ... 还有 {len(statistics['unique_values']) - 10} 个")

            return True

        except Exception as e:
            logger.error(f"生成类别统计失败: {e}")
            return False

    def load_predefined_translations(self) -> bool:
        """
        从category_translations.md文件加载预定义的翻译 (Load Predefined Translations)
        
        Returns:
            bool: 加载是否成功
        """
        try:
            logger.info("开始加载预定义翻译")
            
            # 根据category_translations.md中的标准翻译
            predefined_translations = {
                '规则类型': {
                    '内容完整性': 'ContentCompleteness',
                    '数据一致性': 'DataConsistency',
                    '时效性': 'Timeliness',
                    '术语规范性': 'TerminologyStandardization',
                    '段落完整性': 'SectionCompleteness',
                    '病历管理指标': 'MedicalRecordManagementIndicator',
                    '签名合理性': 'SignatureCompliance',
                    '雷同率': 'DuplicateRate'
                },
                '分类': {
                    '手术科室': 'SurgeryDepartment',
                    '病历首页': 'MedicalRecordFrontPage',
                    '非手术科室': 'NonsurgicalDepartment'
                },
                '所属项目': {
                    '入院记录': 'AdmissionRecord',
                    '出院(死亡)记录': 'Discharge(death)Record',
                    '出院记录': 'DischargeSummary',
                    '医嘱': 'MedicalOrder',
                    '告知书委托书': 'InformedConsentandPowerofAttorney',
                    '死亡记录': 'DeathRecord',
                    '病历书写要求': 'MedicalRecordDocumentationRequirements',
                    '病历管理': 'MedicalRecordManagement',
                    '病历要求': 'MedicalRecordRequirements',
                    '病历首页基本信息': 'MedicalRecordFrontPageBasicInformation',
                    '病历首页诊疗信息': 'MedicalRecordFrontPageClinicalInformation',
                    '病程记录': 'ProgressNotes',
                    '知情同意书': 'InformedConsentForm',
                    '费用信息': 'CostInformation',
                    '辅助检查': 'AncillaryTests'
                }
            }
            
            # 文书类型翻译（部分示例，完整列表见category_translations.md）
            document_type_translations = {
                '24小时内入院死亡记录': '24hourAdmissionandDeathRecord',
                '24小时内出入院记录': 'Within24HoursAdmissionandDischargeRecord',
                '个人信息': 'PersonalInformation',
                '主治医师日常查房记录': 'AttendingPhysicianDailyRoundsRecord',
                '主治医师首次查房记录': 'AttendingPhysicianInitialRoundingRecord',
                '交班记录': 'ShiftHandoverRecord',
                '会诊申请记录': 'ConsultationRequestRecord',
                '会诊病程记录': 'ConsultationProgressNote',
                '住院情况': 'HospitalizationStatus',
                '使用自费项目知情同意书': 'InformedConsentFormforOutofpocketItems',
                '入院记录': 'AdmissionRecord',
                '入院诊断': 'AdmissionDiagnosis',
                '其他(再住院计划)': 'Other(readmissionPlan)',
                '其他(尸检)': 'Other(autopsy)',
                '其他(抢救)': 'Other(rescue)',
                '其他(离院方式)': 'Other(dischargeMethod)',
                '其他(药物过敏)': 'Other(drugAllergy)',
                '其他(血型输血反应)': 'Other(bloodGroupTransfusionReaction)',
                '其他(颅脑损伤昏迷)': 'Other(cranialBrainInjuryComa)',
                '出院记录': 'DischargeSummary',
                '出院诊断(主要诊断)': 'DischargeDiagnosis(primaryDiagnosis)',
                '出院诊断(其他诊断)': 'DischargeDiagnosis(otherDiagnoses)',
                '出院诊断(损伤中毒)': 'DischargeDiagnosis(injuryandPoisoning)',
                '出院诊断(病理诊断)': 'DischargeDiagnosis(pathologicalDiagnosis)',
                '化疗知情同意书': 'ChemotherapyInformedConsentForm',
                '医嘱': 'MedicalOrder',
                '危重病例主任查房记录': 'CriticalCaseAttendingPhysicianRoundingRecord',
                '告知委托书': 'PowerofAttorney',
                '孕产妇与新生儿': 'PregnantWomenandNewborns',
                '手术与操作(主要手术操作)': 'SurgeryandProcedures(primarySurgeryandProcedures)',
                '手术与操作(其他手术操作)': 'SurgicalandProcedural(otherSurgicalProcedures)',
                '手术同意书': 'SurgicalConsentForm',
                '手术安全核查表': 'SurgicalSafetyChecklist',
                '手术记录': 'SurgicalRecord',
                '抢救记录': 'ResuscitationRecord',
                '接班记录': 'ShiftTakeoverRecord',
                '放疗知情同意书': 'RadiotherapyInformedConsentForm',
                '新技术知情同意书': 'NewTechnologyInformedConsentForm',
                '日常病程记录': 'DailyProgressNote',
                '有创诊疗操作记录': 'InvasiveProcedureRecord',
                '术前小结': 'PreoperativeSummary',
                '术前讨论记录': 'PreoperativeDiscussionRecord',
                '术后三天病程记录': 'PostoperativeThreedayCourseRecord',
                '术后首次病程记录': 'PostoperativeInitialProgressNote',
                '死亡病例讨论记录': 'DeathCaseDiscussionRecord',
                '死亡记录': 'DeathRecord',
                '特殊检查（特殊治疗）同意书': 'SpecialExamination(specialTreatment)ConsentForm',
                '电子病历': 'ElectronicMedicalRecord',
                '疑难病例主任查房记录': 'ComplexCaseChiefRoundsRecord',
                '疑难病例讨论记录': 'ComplexCaseDiscussionRecord',
                '病危（重）通知书': 'CriticalIllnessNotification',
                '病历书写': 'MedicalRecordWriting',
                '病历质量评价考核': 'MedicalRecordQualityAssessment',
                '病案质控': 'MedicalRecordQualityControl',
                '知情同意书': 'InformedConsentForm',
                '签名': 'Signature',
                '联系信息': 'ContactInformation',
                '自动出院知情同意书': 'InformedConsentFormforAutomaticDischarge',
                '诊断符合情况': 'DiagnosticAgreement',
                '费用信息': 'CostInformation',
                '转入记录': 'TransferInRecord',
                '转出记录': 'TransferOutRecord',
                '辅助检查': 'AncillaryTests',
                '输血治疗知情同意书': 'BloodTransfusionTherapyInformedConsentForm',
                '输血记录': 'BloodTransfusionRecord',
                '门急诊诊断': 'OutpatientandEmergencyDiagnosis',
                '阶段小结': 'StageSummary',
                '首次病程记录': 'InitialProgressNote',
                '麻醉术前访视记录': 'AnesthesiaPreoperativeVisitRecord',
                '麻醉术后访视记录': 'AnesthesiaPostoperativeVisitRecord',
                '麻醉知情同意书': 'AnesthesiaInformedConsentForm',
                '麻醉记录': 'AnesthesiaRecord',
                '（副）主任医师日常查房记录': 'DeputyChiefPhysicianDailyRoundsRecord',
                '（副）主任医师首次查房记录': 'ChiefPhysicianInitialRoundsRecord'
            }
            
            predefined_translations['文书类型'] = document_type_translations
            
            # 将预定义翻译应用到实际数据中
            self.translations = {}
            for category in self.translation_categories:
                if category in predefined_translations:
                    category_translations = {}
                    # 只为实际存在的值添加翻译
                    if category in self.category_statistics:
                        for chinese_value in self.category_statistics[category]['unique_values']:
                            if chinese_value in predefined_translations[category]:
                                category_translations[chinese_value] = predefined_translations[category][chinese_value]
                            else:
                                # 如果没有预定义翻译，使用原文
                                category_translations[chinese_value] = chinese_value
                                logger.warning(f"未找到 '{chinese_value}' 的预定义翻译，使用原文")
                    
                    self.translations[category] = category_translations
                else:
                    logger.warning(f"未找到类别 '{category}' 的预定义翻译")
            
            logger.info("预定义翻译加载完成")
            return True
            
        except Exception as e:
            logger.error(f"加载预定义翻译失败: {e}")
            return False

    def generate_json_files(self, output_directory: str = "Medical_Record_List_Json") -> bool:
        """
        按文书类型生成JSON文件 (Generate JSON Files by Document Type)

        Args:
            output_directory (str): 输出目录

        Returns:
            bool: 生成是否成功
        """
        try:
            if not self.processed_data:
                logger.error("处理后的数据为空，请先提取数据")
                return False

            if not self.translations:
                logger.error("翻译数据为空，请先加载预定义翻译")
                return False

            logger.info(f"开始生成JSON文件到目录: {os.path.abspath(output_directory)}")

            # 确保输出目录存在
            if not os.path.exists(output_directory):
                os.makedirs(output_directory)
                logger.info(f"创建JSON输出目录: {output_directory}")

            # 按文书类型分组数据
            document_type_groups = {}
            for record in self.processed_data:
                document_type = str(record.get('文书类型', '')).strip()
                if document_type and document_type != 'nan':
                    if document_type not in document_type_groups:
                        document_type_groups[document_type] = []
                    document_type_groups[document_type].append(record)

            logger.info(f"找到 {len(document_type_groups)} 种文书类型")

            # 为每种文书类型生成JSON文件
            generated_files = []
            for document_type, records in document_type_groups.items():
                try:
                    # 获取文书类型的英文翻译作为文件名
                    english_filename = self.translations.get('文书类型', {}).get(document_type, document_type)

                    # 清理文件名，移除特殊字符
                    safe_filename = self._clean_filename(english_filename)
                    json_filename = f"{safe_filename}.json"
                    json_file_path = os.path.join(output_directory, json_filename)

                    # 准备JSON数据
                    json_data = {
                        "metadata": {
                            "document_type_chinese": document_type,
                            "document_type_english": english_filename,
                            "total_records": len(records),
                            "generated_time": datetime.now().isoformat(),
                            "version": "1.0"
                        },
                        "translations": {
                            category: translation_dict for category, translation_dict in self.translations.items()
                        },
                        "records": []
                    }

                    # 处理每条记录
                    for record in records:
                        processed_record = {
                            "rule_id": f"rule_{uuid.uuid4().hex[:8]}",  # 添加唯一的规则ID（短格式）
                            "rule_type_chinese": record.get('规则类型', ''),
                            "rule_type_english": self.translations.get('规则类型', {}).get(
                                str(record.get('规则类型', '')), str(record.get('规则类型', ''))
                            ),
                            "classification_chinese": record.get('分类', ''),
                            "classification_english": self.translations.get('分类', {}).get(
                                str(record.get('分类', '')), str(record.get('分类', ''))
                            ),
                            "belonging_project_chinese": record.get('所属项目', ''),
                            "belonging_project_english": self.translations.get('所属项目', {}).get(
                                str(record.get('所属项目', '')), str(record.get('所属项目', ''))
                            ),
                            "document_type_chinese": record.get('文书类型', ''),
                            "document_type_english": self.translations.get('文书类型', {}).get(
                                str(record.get('文书类型', '')), str(record.get('文书类型', ''))
                            ),
                            "rule_content": record.get('规则内容', ''),
                            "deduction_points": record.get('扣分', 0)
                        }
                        json_data["records"].append(processed_record)

                    # 保存JSON文件
                    with open(json_file_path, 'w', encoding='utf-8') as file:
                        json.dump(json_data, file, ensure_ascii=False, indent=2)

                    generated_files.append(json_file_path)
                    logger.info(f"生成JSON文件: {json_file_path} ({len(records)} 条记录)")

                except Exception as e:
                    logger.error(f"生成文书类型 '{document_type}' 的JSON文件时出错: {e}")

            logger.info(f"成功生成 {len(generated_files)} 个JSON文件")
            return True

        except Exception as e:
            logger.error(f"生成JSON文件失败: {e}")
            return False

    def _clean_filename(self, filename: str) -> str:
        """
        清理文件名，移除特殊字符 (Clean Filename)

        Args:
            filename (str): 原始文件名

        Returns:
            str: 清理后的文件名
        """
        # 移除或替换特殊字符
        import re
        # 保留字母、数字、下划线、连字符和空格
        cleaned_name = re.sub(r'[^\w\s-]', '', filename)
        # 将空格替换为下划线
        cleaned_name = re.sub(r'\s+', '_', cleaned_name)
        # 移除多余的下划线
        cleaned_name = re.sub(r'_+', '_', cleaned_name)
        # 移除开头和结尾的下划线
        cleaned_name = cleaned_name.strip('_')

        return cleaned_name if cleaned_name else "unknown_document_type"

    def generate_excel_statistics(self, output_directory: str) -> bool:
        """
        生成Excel统计表 (Generate Excel Statistics)

        Args:
            output_directory (str): 输出目录

        Returns:
            bool: 生成是否成功
        """
        try:
            if not self.translations:
                logger.error("翻译数据为空，请先加载预定义翻译")
                return False

            logger.info("开始生成Excel统计表")

            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_filename = f"category_translations_{timestamp}.xlsx"
            excel_file_path = os.path.join(output_directory, excel_filename)

            # 创建Excel工作簿
            workbook = openpyxl.Workbook()

            # 删除默认工作表
            workbook.remove(workbook.active)

            # 创建汇总工作表
            summary_sheet = workbook.create_sheet("汇总信息", 0)
            self._create_summary_sheet(summary_sheet)

            # 为每个类别创建工作表
            for category, translation_dict in self.translations.items():
                sheet_name = category.replace('/', '_')  # 避免工作表名称中的特殊字符
                sheet = workbook.create_sheet(sheet_name)
                self._create_category_sheet(sheet, category, translation_dict)

            # 保存Excel文件
            workbook.save(excel_file_path)
            logger.info(f"Excel统计表已保存到: {excel_file_path}")

            return True

        except Exception as e:
            logger.error(f"生成Excel统计表失败: {e}")
            return False

    def _create_summary_sheet(self, sheet):
        """创建汇总信息工作表 (Create Summary Sheet)"""
        # 设置标题
        sheet['A1'] = "医疗记录翻译统计汇总"
        sheet['A1'].font = Font(size=16, bold=True)
        sheet['A1'].alignment = Alignment(horizontal='center')
        sheet.merge_cells('A1:C1')

        # 设置生成时间
        sheet['A3'] = "生成时间:"
        sheet['B3'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 设置总记录数
        sheet['A4'] = "总记录数:"
        sheet['B4'] = len(self.processed_data) if self.processed_data else 0

        # 设置类别统计标题
        sheet['A6'] = "类别统计"
        sheet['A6'].font = Font(size=14, bold=True)

        # 设置表头
        headers = ["序号", "类别名称", "唯一值数量"]
        for column_index, header in enumerate(headers, 1):
            cell = sheet.cell(row=7, column=column_index, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal='center')

        # 填充数据
        row_index = 8
        for index, (category, translation_dict) in enumerate(self.translations.items(), 1):
            sheet.cell(row=row_index, column=1, value=index)
            sheet.cell(row=row_index, column=2, value=category)
            sheet.cell(row=row_index, column=3, value=len(translation_dict))
            row_index += 1

        # 调整列宽
        sheet.column_dimensions['A'].width = 8
        sheet.column_dimensions['B'].width = 20
        sheet.column_dimensions['C'].width = 15

    def _create_category_sheet(self, sheet, category: str, translation_dict: Dict[str, str]):
        """创建类别工作表 (Create Category Sheet)"""
        # 设置标题
        sheet['A1'] = f"{category} - 翻译对照表"
        sheet['A1'].font = Font(size=14, bold=True)
        sheet['A1'].alignment = Alignment(horizontal='center')
        sheet.merge_cells('A1:C1')

        # 设置表头
        headers = ["序号", "中文原文", "英文翻译"]
        for column_index, header in enumerate(headers, 1):
            cell = sheet.cell(row=3, column=column_index, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal='center')

        # 按中文原文排序
        sorted_items = sorted(translation_dict.items(), key=lambda x: x[0])

        # 填充数据
        for index, (chinese_text, english_text) in enumerate(sorted_items, 1):
            sheet.cell(row=index+3, column=1, value=index)
            sheet.cell(row=index+3, column=2, value=chinese_text)
            sheet.cell(row=index+3, column=3, value=english_text)

        # 调整列宽
        sheet.column_dimensions['A'].width = 8
        sheet.column_dimensions['B'].width = 30
        sheet.column_dimensions['C'].width = 40

    def generate_markdown_statistics(self, output_directory: str) -> bool:
        """
        生成Markdown统计表 (Generate Markdown Statistics)

        Args:
            output_directory (str): 输出目录

        Returns:
            bool: 生成是否成功
        """
        try:
            if not self.translations:
                logger.error("翻译数据为空，请先加载预定义翻译")
                return False

            logger.info("开始生成Markdown统计表")

            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            markdown_filename = f"category_translations_{timestamp}.md"
            markdown_file_path = os.path.join(output_directory, markdown_filename)

            # 生成Markdown内容
            markdown_content = self._generate_markdown_content()

            # 保存Markdown文件
            with open(markdown_file_path, 'w', encoding='utf-8') as file:
                file.write(markdown_content)

            logger.info(f"Markdown统计表已保存到: {markdown_file_path}")
            return True

        except Exception as e:
            logger.error(f"生成Markdown统计表失败: {e}")
            return False

    def _generate_markdown_content(self) -> str:
        """生成Markdown内容 (Generate Markdown Content)"""
        content_lines = []

        # 文件标题和生成时间
        content_lines.append("# 医疗记录翻译统计表")
        content_lines.append("")
        content_lines.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_lines.append("")

        # 统计摘要
        content_lines.append("## 统计摘要")
        content_lines.append("")
        content_lines.append(f"- **总记录数**: {len(self.processed_data) if self.processed_data else 0}")
        content_lines.append(f"- **翻译类别数**: {len(self.translations)}")
        content_lines.append("")

        # 类别统计表
        content_lines.append("### 类别统计")
        content_lines.append("")
        content_lines.append("| 序号 | 类别名称 |")
        content_lines.append("|------|----------|")

        for index, (category, translation_dict) in enumerate(self.translations.items(), 1):
            content_lines.append(f"| {index} | {category} |")

        content_lines.append("")

        # 为每个类别创建详细表格
        for category, translation_dict in self.translations.items():
            content_lines.append(f"## {category} - 翻译对照表")
            content_lines.append("")
            content_lines.append("| 中文原文 | 英文翻译 |")
            content_lines.append("|----------|----------|")

            # 按中文原文排序
            sorted_items = sorted(translation_dict.items(), key=lambda x: x[0])

            for chinese_text, english_text in sorted_items:
                # 转义Markdown特殊字符
                chinese_escaped = chinese_text.replace('|', '\\|').replace('\n', ' ')
                english_escaped = english_text.replace('|', '\\|').replace('\n', ' ')
                content_lines.append(f"| {chinese_escaped} | {english_escaped} |")

            content_lines.append("")

        return '\n'.join(content_lines)

    def process_excel_file(self, excel_file_path: str, output_directory: str = ".") -> bool:
        """
        完整处理Excel文件的主方法 (Main Method to Process Excel File)

        Args:
            excel_file_path (str): Excel文件路径
            output_directory (str): 输出目录

        Returns:
            bool: 处理是否成功
        """
        try:
            logger.info("=" * 60)
            logger.info("开始处理医疗记录Excel文件")
            logger.info("=" * 60)

            # Step 1: 读取Excel文件
            logger.info("Step 1: 读取Excel文件")
            if not self.read_excel_file(excel_file_path):
                return False

            # Step 2: 验证并提取数据
            logger.info("Step 2: 验证并提取数据")
            if not self.validate_and_extract_data():
                return False

            # Step 3: 生成类别统计
            logger.info("Step 3: 生成类别统计")
            if not self.generate_category_statistics():
                return False

            # Step 4: 加载预定义翻译（替代LLM翻译）
            logger.info("Step 4: 加载预定义翻译")
            if not self.load_predefined_translations():
                return False

            # Step 5: 生成JSON文件
            logger.info("Step 5: 生成JSON文件")
            json_output_directory = "Medical_Record_List_Json"
            if not self.generate_json_files(json_output_directory):
                return False

            # Step 6: 生成Excel统计表
            logger.info("Step 6: 生成Excel统计表")
            if not self.generate_excel_statistics(output_directory):
                logger.warning("Excel统计表生成失败，但不影响主流程")

            # Step 7: 生成Markdown统计表
            logger.info("Step 7: 生成Markdown统计表")
            if not self.generate_markdown_statistics(output_directory):
                logger.warning("Markdown统计表生成失败，但不影响主流程")

            logger.info("=" * 60)
            logger.info("医疗记录Excel文件处理完成！")
            logger.info("=" * 60)

            return True

        except Exception as e:
            logger.error(f"处理Excel文件时出现错误: {e}")
            return False

    def get_processing_summary(self) -> Dict[str, Any]:
        """
        获取处理摘要信息 (Get Processing Summary)

        Returns:
            Dict[str, Any]: 处理摘要
        """
        summary = {
            "total_records": len(self.processed_data) if self.processed_data else 0,
            "category_statistics": {},
            "translation_statistics": {},
            "document_types": []
        }

        # 类别统计
        for category, statistics in self.category_statistics.items():
            summary["category_statistics"][category] = {
                "unique_count": statistics["count"],
                "sample_values": statistics["unique_values"][:5]  # 前5个示例
            }

        # 翻译统计
        for category, translation_dict in self.translations.items():
            summary["translation_statistics"][category] = len(translation_dict)

        # 文书类型
        if self.processed_data:
            document_types = set()
            for record in self.processed_data:
                document_type = str(record.get('文书类型', '')).strip()
                if document_type and document_type != 'nan':
                    document_types.add(document_type)
            summary["document_types"] = sorted(list(document_types))

        return summary


def _find_excel_file() -> str:
    """
    智能查找Excel文件路径 (Smart Excel File Path Finder)

    Returns:
        str: Excel文件的正确路径，如果找不到则返回空字符串
    """
    excel_filename = "附件1. 质控评分表v2.0.xlsx"

    # 可能的路径列表，按优先级排序
    possible_paths = [
        # 从主目录运行
        f"doc/{excel_filename}",
        f"../doc/{excel_filename}",
        f"../../doc/{excel_filename}",

        # 从tests目录运行
        f"../doc/{excel_filename}",
        f"../../doc/{excel_filename}",
        f"../../../doc/{excel_filename}",

        # 从更深层目录运行
        f"../../../../doc/{excel_filename}",
        f"../../../../../doc/{excel_filename}",

        # 绝对路径尝试（基于当前工作目录）
        os.path.join(os.getcwd(), "doc", excel_filename),
        os.path.join(os.path.dirname(os.getcwd()), "doc", excel_filename),
        os.path.join(os.path.dirname(os.path.dirname(os.getcwd())), "doc", excel_filename),

        # 直接在当前目录查找
        excel_filename,
        f"./{excel_filename}",
    ]

    # 尝试每个可能的路径
    for path in possible_paths:
        if os.path.exists(path):
            return os.path.abspath(path)

    return ""

def _get_default_excel_path() -> Tuple[str, str]:
    """
    获取默认的Excel文件路径和显示路径 (Get Default Excel Path)

    Returns:
        Tuple[str, str]: (实际路径, 显示给用户的路径)
    """
    # 首先尝试智能查找
    found_path = _find_excel_file()
    if found_path:
        # 计算相对路径用于显示
        try:
            relative_path = os.path.relpath(found_path)
            return found_path, relative_path
        except ValueError:
            return found_path, found_path

    # 如果找不到，返回最可能的默认路径
    current_directory = os.getcwd()
    if "tests" in current_directory.lower():
        default_display = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    elif "Medical_Record_List" in current_directory:
        default_display = "../../doc/附件1. 质控评分表v2.0.xlsx"
    else:
        default_display = "doc/附件1. 质控评分表v2.0.xlsx"

    return default_display, default_display

def get_user_input() -> Tuple[str, str]:
    """
    获取用户输入 (Get User Input)

    Returns:
        Tuple[str, str]: (文件路径, 输出目录)
    """
    print("=" * 60)
    print("医疗记录列表生成器 (Medical Record List Generator)")
    print("=" * 60)

    # 获取默认Excel文件路径
    default_path, display_path = _get_default_excel_path()

    # 获取Excel文件路径
    while True:
        if os.path.exists(default_path):
            prompt = f"请输入Excel文件路径 (默认: {display_path}): "
            status = "✅ 文件已找到"
        else:
            prompt = f"请输入Excel文件路径 (建议: {display_path}): "
            status = "⚠️ 默认路径不存在，请手动输入"

        print(f"当前工作目录: {os.getcwd()}")
        print(f"状态: {status}")

        file_path = input(prompt).strip()
        if not file_path:
            file_path = default_path

        # 尝试智能查找用户输入的文件
        if not os.path.exists(file_path):
            # 如果用户输入的路径不存在，尝试智能查找
            if os.path.basename(file_path) == "附件1. 质控评分表v2.0.xlsx":
                smart_path = _find_excel_file()
                if smart_path:
                    print(f"💡 智能找到文件: {smart_path}")
                    file_path = smart_path

        if os.path.exists(file_path):
            print(f"✅ 使用文件: {os.path.abspath(file_path)}")
            break
        else:
            print(f"❌ 文件不存在: {file_path}")
            print("\n可能的解决方案:")
            print("1. 检查文件名是否正确")
            print("2. 尝试以下路径之一:")

            # 显示一些可能的路径建议
            path_suggestions = [
                "doc/附件1. 质控评分表v2.0.xlsx",
                "../doc/附件1. 质控评分表v2.0.xlsx",
                "../../doc/附件1. 质控评分表v2.0.xlsx",
                "../../../doc/附件1. 质控评分表v2.0.xlsx"
            ]

            for index, suggestion in enumerate(path_suggestions, 1):
                exists_status = "✅" if os.path.exists(suggestion) else "❌"
                print(f"   {exists_status} {suggestion}")

            print("3. 使用绝对路径")
            print("请重新输入正确的文件路径\n")

    # 获取输出目录
    output_directory = input("请输入输出目录 (默认: 当前目录): ").strip()
    if not output_directory:
        output_directory = "."

    return file_path, output_directory


def main():
    """主函数 (Main Function)"""
    try:
        # 获取用户输入
        file_path, output_directory = get_user_input()

        # 创建生成器实例
        generator = MedicalRecordListGenerator()

        # 处理Excel文件（不使用LLM，直接使用预定义翻译）
        success = generator.process_excel_file(file_path, output_directory)

        if success:
            # 显示处理摘要
            summary = generator.get_processing_summary()
            print("\n" + "=" * 60)
            print("处理摘要 (Processing Summary)")
            print("=" * 60)
            print(f"总记录数: {summary['total_records']}")
            print(f"文书类型数: {len(summary['document_types'])}")

            print("\n类别统计:")
            for category, statistics in summary['category_statistics'].items():
                print(f"  {category}: {statistics['unique_count']} 个唯一值")

            print("\n翻译统计:")
            for category, count in summary['translation_statistics'].items():
                print(f"  {category}: {count} 个翻译")

            print(f"\n文书类型列表:")
            for document_type in summary['document_types'][:10]:  # 显示前10个
                print(f"  - {document_type}")
            if len(summary['document_types']) > 10:
                print(f"  ... 还有 {len(summary['document_types']) - 10} 个")

            print(f"\n📁 文件保存位置:")
            print(f"   JSON文件: {os.path.abspath('Medical_Record_List_Json')}")
            print(f"   统计文件: {os.path.abspath(output_directory)}")
            print("✅ 处理完成！")
        else:
            print("处理失败，请查看日志了解详细信息")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        logger.error(f"主函数执行出错: {e}")
        print(f"执行出错: {e}")


if __name__ == "__main__":
    main()
