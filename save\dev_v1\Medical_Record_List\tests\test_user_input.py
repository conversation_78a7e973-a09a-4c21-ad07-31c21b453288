# -*- coding: utf-8 -*-
"""
测试用户输入功能（非交互式）
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import _find_excel_file, _get_default_excel_path

def simulate_user_input_scenarios():
    """模拟用户输入场景"""
    print("=" * 60)
    print("模拟用户输入场景测试")
    print("=" * 60)
    
    print(f"当前工作目录: {os.getcwd()}")
    
    # 场景1：默认路径可用
    print("\n=== 场景1：默认路径可用 ===")
    default_path, display_path = _get_default_excel_path()
    
    if os.path.exists(default_path):
        print(f"✅ 默认路径可用")
        print(f"   实际路径: {default_path}")
        print(f"   显示路径: {display_path}")
        print(f"   用户体验: 用户可以直接按回车使用默认路径")
    else:
        print(f"❌ 默认路径不可用")
        print(f"   实际路径: {default_path}")
        print(f"   显示路径: {display_path}")
        print(f"   用户体验: 需要手动输入路径")
    
    # 场景2：智能查找功能
    print("\n=== 场景2：智能查找功能 ===")
    smart_path = _find_excel_file()
    if smart_path:
        print(f"✅ 智能查找成功")
        print(f"   找到路径: {smart_path}")
        print(f"   用户体验: 即使输入错误路径，程序也能自动找到文件")
    else:
        print(f"❌ 智能查找失败")
        print(f"   用户体验: 需要用户提供准确路径")
    
    # 场景3：路径建议
    print("\n=== 场景3：路径建议 ===")
    excel_filename = "附件1. 质控评分表v2.0.xlsx"
    suggestions = [
        f"doc/{excel_filename}",
        f"../doc/{excel_filename}", 
        f"../../doc/{excel_filename}",
        f"../../../doc/{excel_filename}"
    ]
    
    print("当路径不存在时，程序会提供以下建议:")
    valid_suggestions = []
    for suggestion in suggestions:
        exists = os.path.exists(suggestion)
        status = "✅" if exists else "❌"
        print(f"   {status} {suggestion}")
        if exists:
            valid_suggestions.append(suggestion)
    
    if valid_suggestions:
        print(f"\n✅ 找到 {len(valid_suggestions)} 个有效建议")
        print(f"   用户体验: 用户可以从建议中选择正确路径")
    else:
        print(f"\n❌ 没有有效建议")
        print(f"   用户体验: 用户需要使用绝对路径或检查文件位置")
    
    # 场景4：不同工作目录的适应性
    print("\n=== 场景4：不同工作目录的适应性 ===")
    current_dir = os.getcwd()
    
    if "tests" in current_dir.lower():
        print("✅ 检测到在tests目录中运行")
        print("   程序会自动调整路径建议")
    elif "Medical_Record_List" in current_dir:
        print("✅ 检测到在Medical_Record_List目录中运行")
        print("   程序会使用相应的默认路径")
    else:
        print("✅ 检测到在其他目录中运行")
        print("   程序会尝试多种路径组合")
    
    print("\n=== 总结 ===")
    print("路径处理功能状态:")
    print(f"   智能查找: {'✅ 可用' if smart_path else '❌ 不可用'}")
    print(f"   默认路径: {'✅ 可用' if os.path.exists(default_path) else '❌ 不可用'}")
    print(f"   路径建议: {'✅ 有效' if valid_suggestions else '❌ 无效'}")
    print(f"   目录适应: ✅ 可用")
    
    overall_status = all([
        smart_path,
        os.path.exists(default_path),
        valid_suggestions
    ])
    
    print(f"\n整体状态: {'✅ 优秀' if overall_status else '⚠️ 需要改进'}")
    
    if overall_status:
        print("\n🎉 路径处理功能完全正常！")
        print("   用户可以:")
        print("   - 直接按回车使用默认路径")
        print("   - 输入简单的文件名让程序智能查找")
        print("   - 从错误提示中选择正确的路径建议")
    else:
        print("\n⚠️ 路径处理功能需要改进")
        print("   建议检查文件位置和路径配置")

if __name__ == "__main__":
    simulate_user_input_scenarios()
