# 规则ID: rule_b24ccb01
# 描述: 检查病历中是否缺少诊疗经过段落
# 扣分: 6.0分

def check_rule(medical_record):
    """检查病历是否缺少诊疗经过字段"""
    try:
        # 判断诊疗经过字段是否存在且非空
        if (not isinstance(medical_record, dict) or 
            'treatment_process' not in medical_record or 
            not str(medical_record['treatment_process']).strip()):
            return True
        return False
    except Exception as e:
        print(f"规则执行错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 测试用例1: 正常包含诊疗经过
    test1 = {"treatment_process": "患者入院后完善检查，给予抗感染治疗"}
    print(f"测试用例1结果: {check_rule(test1)}")  # 预期输出: False
    
    # 测试用例2: 缺失诊疗经过字段
    test2 = {"diagnosis": "肺炎"}
    print(f"测试用例2结果: {check_rule(test2)}")  # 预期输出: True
    
    # 测试用例3: 诊疗经过为空字符串
    test3 = {"treatment_process": ""}
    print(f"测试用例3结果: {check_rule(test3)}")  # 预期输出: True
    
    # 测试用例4: 诊疗经过为None
    test4 = {"treatment_process": None}
    print(f"测试用例4结果: {check_rule(test4)}")  # 预期输出: True
    
    # 测试用例5: 非字典输入
    test5 = "非法输入"
    print(f"测试用例5结果: {check_rule(test5)}")  # 预期输出: False