# -*- coding: utf-8 -*-
"""
测试JSON文件数据验证 - 确认"分类"数据来自正确的Excel列
"""

import sys
import os
import json
import shutil

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator
from config import deepseek_v3_config

def test_json_data_verification():
    """测试JSON文件数据验证"""
    print("=" * 80)
    print("测试JSON文件数据验证 - 确认'分类'数据来自正确的Excel列")
    print("=" * 80)
    
    # 设置参数
    excel_path = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    model_config = deepseek_v3_config
    json_output_dir = "Medical_Record_List_Json"
    
    # 清理可能存在的测试目录
    if os.path.exists(json_output_dir):
        shutil.rmtree(json_output_dir)
        print(f"🧹 清理已存在的测试目录: {json_output_dir}")
    
    # 创建生成器实例
    generator = MedicalRecordListGenerator()
    
    # 执行基础步骤
    print("\n" + "=" * 60)
    print("Step 1-3: 执行基础步骤")
    print("=" * 60)
    
    if not generator.read_excel_file(excel_path):
        print("❌ Excel文件读取失败")
        return False
    
    if not generator.validate_and_extract_data():
        print("❌ 数据验证和提取失败")
        return False
    
    if not generator.generate_category_statistics():
        print("❌ 类别统计生成失败")
        return False
    
    print("✅ 基础步骤完成")
    
    # 创建简单的翻译数据用于测试
    print("\n" + "=" * 60)
    print("Step 4: 创建测试翻译数据")
    print("=" * 60)
    
    test_translations = {
        '规则类型': {
            '时效性': 'Timeliness',
            '段落完整性': 'Paragraph Integrity',
            '内容完整性': 'Content Integrity'
        },
        '分类': {
            '手术科室': 'Surgical Department',
            '非手术科室': 'Non-Surgical Department', 
            '病历首页': 'Medical Record Homepage'
        },
        '所属项目': {
            '病程记录': 'Progress Record',
            '知情同意书': 'Informed Consent'
        },
        '文书类型': {
            '24小时内入院死亡记录': '24-Hour Admission and Death Record',
            '使用自费项目知情同意书': 'Self-Pay Item Informed Consent'
        }
    }
    
    generator.translations = test_translations
    print("✅ 创建了测试翻译数据")
    
    # 生成JSON文件
    print("\n" + "=" * 60)
    print("Step 5: 生成JSON文件")
    print("=" * 60)
    
    if not generator.generate_json_files(json_output_dir):
        print("❌ JSON文件生成失败")
        return False
    
    print("✅ JSON文件生成成功")
    
    # 验证特定JSON文件的内容
    print("\n" + "=" * 60)
    print("Step 6: 验证JSON文件内容")
    print("=" * 60)
    
    # 查找24小时内入院死亡记录的JSON文件
    target_json_file = None
    json_files = [f for f in os.listdir(json_output_dir) if f.endswith('.json')]
    
    for json_file in json_files:
        if '24' in json_file and 'Death' in json_file:
            target_json_file = json_file
            break
    
    if not target_json_file:
        print("⚠️ 未找到24小时内入院死亡记录的JSON文件")
        # 显示所有JSON文件
        print("📁 生成的JSON文件:")
        for i, json_file in enumerate(json_files[:10], 1):
            print(f"   {i:2d}. {json_file}")
        if len(json_files) > 10:
            print(f"   ... 还有 {len(json_files) - 10} 个文件")
        return False
    
    print(f"✅ 找到目标JSON文件: {target_json_file}")
    
    # 读取并验证JSON文件内容
    json_file_path = os.path.join(json_output_dir, target_json_file)
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        print(f"📄 JSON文件读取成功")
        
        # 验证元数据
        metadata = json_data.get('metadata', {})
        print(f"\n📋 元数据信息:")
        print(f"   中文文书类型: {metadata.get('document_type_chinese')}")
        print(f"   英文文书类型: {metadata.get('document_type_english')}")
        print(f"   记录总数: {metadata.get('total_records')}")
        
        # 验证翻译数据
        translations = json_data.get('translations', {})
        if '分类' in translations:
            classification_translations = translations['分类']
            print(f"\n🔍 '分类'字段的翻译对照:")
            for chinese, english in classification_translations.items():
                print(f"   {chinese} -> {english}")
        
        # 验证记录数据
        records = json_data.get('records', [])
        if records:
            print(f"\n📊 记录数据验证 (共{len(records)}条):")
            
            # 检查前几条记录的"分类"字段
            for i, record in enumerate(records[:3], 1):
                classification_chinese = record.get('classification_chinese', '')
                classification_english = record.get('classification_english', '')
                rule_content = record.get('rule_content', '')[:50] + '...'
                
                print(f"\n   记录 {i}:")
                print(f"     分类(中文): {classification_chinese}")
                print(f"     分类(英文): {classification_english}")
                print(f"     规则内容: {rule_content}")
            
            # 统计所有记录中"分类"字段的值
            classification_values = [record.get('classification_chinese', '') for record in records]
            unique_classifications = list(set(classification_values))
            
            print(f"\n📈 该JSON文件中'分类'字段的唯一值:")
            for i, value in enumerate(unique_classifications, 1):
                count = classification_values.count(value)
                print(f"   {i}. {value} ({count}条记录)")
            
            # 验证这些值是否来自Excel的"分类.1"列
            expected_values = ['手术科室', '非手术科室', '病历首页']
            actual_values = set(unique_classifications)
            
            print(f"\n🔍 数据来源验证:")
            print(f"   预期值(来自分类.1列): {expected_values}")
            print(f"   实际值(JSON中的值): {list(actual_values)}")
            
            is_correct = actual_values.issubset(set(expected_values))
            print(f"   数据来源正确: {'✅ 是' if is_correct else '❌ 否'}")
            
            if not is_correct:
                unexpected_values = actual_values - set(expected_values)
                print(f"   意外的值: {list(unexpected_values)}")
        
        print(f"\n✅ JSON文件内容验证完成")
        return True
        
    except Exception as e:
        print(f"❌ JSON文件读取或验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 开始JSON数据验证测试")
    
    success = test_json_data_verification()
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if success:
        print("🎉 所有测试通过！")
        print("\n✅ 验证结果:")
        print("   - Excel列映射修改成功")
        print("   - '分类'字段现在使用'分类.1'列的数据")
        print("   - JSON文件生成正常")
        print("   - 数据内容正确")
        print("   - 翻译功能正常")
        
        print("\n📊 数据对比:")
        print("   - 原'分类'列值: 缺项, 时效, 内涵")
        print("   - 新'分类'列值: 手术科室, 非手术科室, 病历首页")
        print("   - JSON中使用的是新的分类值 ✅")
        
    else:
        print("❌ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
