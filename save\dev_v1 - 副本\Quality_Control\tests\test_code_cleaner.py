# -*- coding: utf-8 -*-
"""
测试代码清理工具
演示如何清理生成的Python文件中的格式问题
"""
import sys
from pathlib import Path

# 添加父目录路径以导入质控模块
sys.path.append(str(Path(__file__).parent.parent))

from code_cleaner import CodeCleaner

def create_test_file_with_issues():
    """创建一个包含格式问题的测试文件"""
    test_content = '''```python
# -*- coding: utf-8 -*-
"""
质控规则检查代码：测试规则
"""

import re
from typing import Dict, Any

def check_rule(medical_record: Dict[str, Any]) -> bool:
    """
    检查测试规则
    """
    return False

这个代码实现了以下功能：

1. 检查病历数据的完整性
2. 返回布尔值结果
3. 包含异常处理

主要功能说明：
- 函数接收病历数据作为输入
- 进行规则检查
- 返回检查结果

使用方法：
直接调用check_rule函数即可。

if __name__ == "__main__":
    # 测试代码
    test_record = {"content": "测试内容"}
    result = check_rule(test_record)
    print(f"检查结果: {result}")

这个实现考虑了多种可能的情况，能够全面地检查质控规则。
```'''
    
    test_file = Path(__file__).parent / "test_problematic_code.py"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    return test_file

def test_code_cleaning():
    """测试代码清理功能"""
    print("=" * 80)
    print("测试代码清理功能")
    print("=" * 80)
    
    # 创建测试文件
    test_file = create_test_file_with_issues()
    print(f"创建测试文件: {test_file}")
    
    # 显示原始内容
    print("\n原始文件内容:")
    print("-" * 40)
    with open(test_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
        print(original_content)
    
    # 清理文件
    print("\n开始清理文件...")
    print("-" * 40)
    cleaner = CodeCleaner()
    success = cleaner.clean_file(test_file)
    
    if success:
        print("\n清理后的文件内容:")
        print("-" * 40)
        with open(test_file, 'r', encoding='utf-8') as f:
            cleaned_content = f.read()
            print(cleaned_content)
        
        print("\n清理效果对比:")
        print(f"原始文件行数: {len(original_content.split('\\n'))}")
        print(f"清理后行数: {len(cleaned_content.split('\\n'))}")
        
        # 检查是否还有问题
        has_markdown = '```' in cleaned_content
        has_description = any(line.strip().startswith('这个代码实现了') or 
                            line.strip().startswith('主要功能') or
                            line.strip().startswith('使用方法')
                            for line in cleaned_content.split('\\n'))
        
        print(f"是否还有Markdown标记: {'是' if has_markdown else '否'}")
        print(f"是否还有描述文字: {'是' if has_description else '否'}")
        
        if not has_markdown and not has_description:
            print("✓ 清理成功！文件现在只包含纯Python代码")
        else:
            print("✗ 清理不完全，仍有格式问题")
    else:
        print("清理失败或文件无需清理")
    
    # 清理测试文件
    if test_file.exists():
        test_file.unlink()
        backup_file = test_file.with_suffix(test_file.suffix + '.backup')
        if backup_file.exists():
            backup_file.unlink()

def test_directory_cleaning():
    """测试目录清理功能"""
    print("\n" + "=" * 80)
    print("测试目录清理功能")
    print("=" * 80)
    
    cleaner = CodeCleaner()
    
    # 检查规则质控目录
    regulatory_dir = Path(__file__).parent / "Regulatory_Quality_Control"
    if regulatory_dir.exists():
        print(f"检查规则质控目录: {regulatory_dir}")
        python_files = list(regulatory_dir.glob("**/*.py"))
        print(f"找到 {len(python_files)} 个Python文件")
        
        if python_files:
            print("文件列表:")
            for i, file_path in enumerate(python_files[:10], 1):  # 只显示前10个
                print(f"  {i}. {file_path.relative_to(regulatory_dir)}")
            if len(python_files) > 10:
                print(f"  ... 还有 {len(python_files) - 10} 个文件")
    else:
        print("规则质控目录不存在")
    
    # 检查内涵质控目录
    connotation_dir = Path(__file__).parent / "Connotation_Quality_Control"
    if connotation_dir.exists():
        print(f"\\n检查内涵质控目录: {connotation_dir}")
        python_files = list(connotation_dir.glob("**/*.py"))
        print(f"找到 {len(python_files)} 个Python文件")
        
        if python_files:
            print("文件列表:")
            for i, file_path in enumerate(python_files[:10], 1):  # 只显示前10个
                print(f"  {i}. {file_path.relative_to(connotation_dir)}")
            if len(python_files) > 10:
                print(f"  ... 还有 {len(python_files) - 10} 个文件")
    else:
        print("内涵质控目录不存在")

def demonstrate_cleaning_patterns():
    """演示清理模式识别"""
    print("\n" + "=" * 80)
    print("演示清理模式识别")
    print("=" * 80)
    
    cleaner = CodeCleaner()
    
    test_lines = [
        "```python",
        "```",
        "# 这是注释",
        "import os",
        "def check_rule():",
        "这个代码实现了以下功能：",
        "主要功能说明：",
        "使用方法：",
        "1. 检查病历数据",
        "    return True",
        '"""这是文档字符串"""',
        "print('Hello World')",
        "这个实现考虑了多种可能的情况",
        "if __name__ == '__main__':"
    ]
    
    print("测试行分类结果:")
    print("-" * 40)
    
    for line in test_lines:
        is_markdown = cleaner.is_markdown_block_marker(line)
        is_description = cleaner.is_chinese_description(line)
        
        status = []
        if is_markdown:
            status.append("Markdown标记")
        if is_description:
            status.append("中文描述")
        if not status:
            status.append("保留")
        
        print(f"'{line:30}' -> {', '.join(status)}")

def main():
    """主函数"""
    print("代码清理工具测试")
    print("=" * 80)
    
    # 运行各项测试
    test_code_cleaning()
    test_directory_cleaning()
    demonstrate_cleaning_patterns()
    
    print("\n" + "=" * 80)
    print("使用说明")
    print("=" * 80)
    print("1. 清理单个文件:")
    print("   python code_cleaner.py --file path/to/file.py")
    print()
    print("2. 清理目录:")
    print("   python code_cleaner.py --dir path/to/directory")
    print()
    print("3. 清理质控系统所有文件:")
    print("   python code_cleaner.py --all")
    print("   或直接运行: python code_cleaner.py")
    print()
    print("4. 在代码生成器中自动清理:")
    print("   重构后的生成器会自动清理生成的代码")
    print("=" * 80)

if __name__ == "__main__":
    main()
