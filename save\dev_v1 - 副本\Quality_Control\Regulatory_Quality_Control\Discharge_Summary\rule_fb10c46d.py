"""
rule_fb10c46d - 签名合理性 (Signature Compliance)
规则内容：缺住院医师、主治医师或以上签名
扣分：3.0分
"""
import sys

def check_rule(medical_record):
    """
    检查病历是否缺少住院医师或主治医师签名
    返回True表示存在签名缺失问题，False表示签名完整
    """
    try:
        # 获取签名字段值并转换为布尔值
        has_resident = bool(medical_record.get('resident_physician', ''))
        has_attending = bool(medical_record.get('attending_physician', ''))
        
        # 如果缺少任一签名则触发规则
        if not (has_resident and has_attending):
            return True
        return False
            
    except (AttributeError, KeyError) as e:
        # 处理非字典输入或字段缺失情况
        print(f"数据结构异常: {e}", file=sys.stderr)
        return False

if __name__ == "__main__":
    # 测试用例1: 签名完整
    test1 = {'resident_physician': '张三', 'attending_physician': '李四'}
    print("Test1:", check_rule(test1))  # 应返回False
    
    # 测试用例2: 缺住院医师签名
    test2 = {'resident_physician': '', 'attending_physician': '李四'}
    print("Test2:", check_rule(test2))  # 应返回True
    
    # 测试用例3: 缺主治医师签名
    test3 = {'resident_physician': '张三', 'attending_physician': ''}
    print("Test3:", check_rule(test3))  # 应返回True
    
    # 测试用例4: 两个签名都缺
    test4 = {'resident_physician': '', 'attending_physician': ''}
    print("Test4:", check_rule(test4))  # 应返回True
    
    # 测试用例5: 非字典输入
    test5 = "invalid"
    print("Test5:", check_rule(test5))  # 应返回False