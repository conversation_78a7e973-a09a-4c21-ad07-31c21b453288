# -*- coding: utf-8 -*-
"""
规则类型分类器
功能：使用大模型判断规则是内涵质控标准还是规则质控标准
"""

import sys
import os
import logging
from typing import Dict, Any, Optional

# 添加父目录到路径以导入model_use和config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model_use import llm_use

# 导入医疗文档标准配置
try:
    from medical_document_standards import (
        classify_rule_by_standards,
        normalize_document_type,
        get_required_sections,
        is_structural_issue,
        is_content_issue
    )
except ImportError:
    # 如果无法导入，定义简单的备用函数
    def classify_rule_by_standards(rule_data: dict) -> str:
        return "内涵"  # 默认分类

# 配置日志
def setup_logger(name: str = __name__, level: int = logging.INFO) -> logging.Logger:
    """
    设置日志配置

    Args:
        name (str): 日志器名称
        level (int): 日志级别

    Returns:
        logging.Logger: 配置好的日志器
    """
    logger = logging.getLogger(name)

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    logger.setLevel(level)

    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器
    try:
        file_handler = logging.FileHandler('rule_type_classifier.log', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        logger.warning(f"无法创建日志文件: {e}")

    return logger

logger = setup_logger()

def classify_rule_type(rule_data: Dict[str, Any], model_config: Optional[Dict[str, Any]] = None) -> str:
    """
    使用优化的分类逻辑判断规则是内涵质控标准还是规则质控标准

    Args:
        rule_data (Dict[str, Any]): 包含规则信息的字典
        model_config (Optional[Dict[str, Any]]): 模型配置，如果为None则使用默认配置

    Returns:
        str: "内涵" 或 "规则"
    """

    rule_identifier = rule_data.get('rule_content', '')[:50] + "..." if len(rule_data.get('rule_content', '')) > 50 else rule_data.get('rule_content', '')
    logger.info(f"开始分类规则: {rule_identifier}")

    # 首先尝试使用标准化分类逻辑
    try:
        standard_classification = classify_rule_by_standards(rule_data)
        logger.info(f"标准化分类结果: {rule_identifier} -> {standard_classification}")

        # 如果标准化分类有明确结果，直接返回
        if standard_classification in ["规则", "内涵", "规则和内涵"]:
            return standard_classification
    except Exception as e:
        logger.warning(f"标准化分类失败: {e}，将使用LLM分类")

    # 如果没有提供模型配置，使用默认配置
    if model_config is None:
        try:
            from config import qwen_32B_config
            model_config = qwen_32B_config
            logger.info("使用默认模型配置: qwen_32B_config")
        except ImportError as e:
            logger.error(f"无法导入默认模型配置: {e}")
            return _get_default_classification(rule_data)

    # 构建优化的系统提示词
    system_prompt = """
你是一个医疗质控专家，需要根据明确的分类标准判断质控规则类型。

## 核心分类标准（三元分类）

### 规则质控 (Rule-based Quality Control) - 结构完整性检查
**触发条件**：医疗文档缺少必需的标准章节或组成部分
**判断流程**：
1. 读取文档类型，对照标准章节要求清单
2. 检查文档是否包含所有必需章节
3. 如发现整个章节缺失，立即分类为规则质控

**典型场景**：
- 首次病程记录缺少"病例特点"章节
- 入院记录缺少"主诉"、"现病史"、"体格检查"等标准章节
- 出院记录缺少"入院诊断"、"出院诊断"等章节
- 时效性问题（如"未在24小时内完成"）
- 签名缺失或格式不规范
- **重要**：即使有条件限制的章节缺失也属于规则质控（如"缺鉴别诊断，诊断不存在待查类的病人，可不判断"）

### 内涵质控 (Content Quality Control) - 内容充分性检查
**触发条件**：文档章节结构完整，但章节内容缺失、不充分或质量不达标
**判断流程**：
1. 确认所有必需章节均已存在
2. 检查各章节内部的必需内容要素
3. 评估内容的医学专业性、完整性和充分性
4. 如发现内容层面问题，分类为内涵质控

**典型场景**：
- "现病史"章节存在但缺少详细的发病时间、症状演变过程描述
- "病例特点"章节存在但内容过于简略，缺少关键临床信息分析
- "体格检查"章节存在但检查项目不全面或描述不具体
- 诊断依据不充分、治疗方案不合理等医疗内容质量问题

### 混合质控 (Mixed Quality Control) - 规则和内涵并存
**触发条件**：规则内容同时包含结构完整性问题和内容充分性问题
**判断流程**：
1. 检查是否存在章节缺失（结构性问题）
2. 检查是否存在具体内容要求不满足（内容性问题）
3. 如两者并存，分类为混合质控

**典型场景**：
- "缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等"
  - 结构问题：缺出院医嘱（整个章节缺失）
  - 内容问题：具体内容要素不充分（药名、剂量等详细要求）

## 文档类型标准化映射
- "首次病程" ↔ "首次病程记录"
- "入院记录" ↔ "入院病历"
- "出院记录" ↔ "出院小结"

## 分类决策树（三元分类）
1. **第一步**：检查是否为混合型问题（同时包含章节缺失和具体内容要求）→ 规则和内涵
2. **第二步**：检查是否为结构层面问题（整个章节缺失，包括带条件限制的章节缺失）→ 规则质控
3. **第三步**：检查是否为时效性、签名、格式问题 → 规则质控
4. **第四步**：检查是否为内容充分性、医疗质量问题 → 内涵质控

## 重要说明
- **混合型识别**：如果规则同时涉及章节缺失和具体内容要求，优先分类为"规则和内涵"
- **章节缺失判断**：只要涉及整个章节的缺失，无论是否有条件限制，都属于结构完整性问题
- **条件性缺失**：如"缺鉴别诊断，诊断不存在待查类的病人，可不判断"仍然是章节缺失，属于规则质控

## 输出要求
只返回"内涵"、"规则"或"规则和内涵"，不要返回其他内容。
    """

    # 构建增强的用户提示词
    rule_content = rule_data.get('rule_content', '')
    rule_type_chinese = rule_data.get('rule_type_chinese', '')
    classification_chinese = rule_data.get('classification_chinese', '')
    document_type_chinese = rule_data.get('document_type_chinese', '')
    belonging_project_chinese = rule_data.get('belonging_project_chinese', '')

    # 标准化文档类型
    try:
        normalized_doc_type = normalize_document_type(document_type_chinese)
        required_sections = get_required_sections(normalized_doc_type)
        sections_info = f"标准章节要求: {', '.join(required_sections)}" if required_sections else "无标准章节信息"
    except:
        normalized_doc_type = document_type_chinese
        sections_info = "无标准章节信息"

    user_prompt = f"""
请根据分类决策树判断以下质控规则的类型：

## 规则信息
- 规则内容：{rule_content}
- 规则类型：{rule_type_chinese}
- 分类：{classification_chinese}
- 文书类型：{document_type_chinese}（标准化：{normalized_doc_type}）
- 所属项目：{belonging_project_chinese}
- {sections_info}

## 分析要点（三元分类）
1. 是否同时包含章节缺失和具体内容要求？（如"缺出院医嘱，出院带药未写明药名、剂量..."）→ 规则和内涵
2. 是否为整个章节缺失？（如"缺主诉"、"缺现病史"等）→ 规则质控
3. 是否为时效性问题？（如"未在24小时内完成"）→ 规则质控
4. 是否为签名格式问题？→ 规则质控
5. 是否为章节内容不充分？（如"描述有缺陷"、"缺发病情况"）→ 内涵质控
6. 是否为医疗质量问题？（如诊断依据不充分）→ 内涵质控

请严格按照决策树进行分类，只返回"内涵"、"规则"或"规则和内涵"。
    """

    rule_identifier = rule_content[:50] + "..." if len(rule_content) > 50 else rule_content
    logger.info(f"开始分类规则: {rule_identifier}")

    try:
        # 调用大模型进行判断
        logger.debug(f"调用模型: {model_config.get('model', 'Unknown')}")
        result = llm_use(system_prompt, user_prompt, model_config)

        if result:
            # 清理结果，支持三元分类
            result = result.strip()
            logger.debug(f"模型原始返回: {result}")

            if "规则和内涵" in result:
                classification = "规则和内涵"
            elif "内涵" in result:
                classification = "内涵"
            elif "规则" in result:
                classification = "规则"
            else:
                # 如果模型返回的结果不明确，使用默认分类逻辑
                classification = _get_default_classification(rule_data)
                logger.warning(f"模型返回结果不明确: {result}，使用默认分类: {classification}")
        else:
            # 如果模型调用失败，使用默认分类逻辑
            classification = _get_default_classification(rule_data)
            logger.error(f"模型调用失败，使用默认分类: {classification}")

        logger.info(f"规则分类完成: {rule_identifier} -> {classification}")
        return classification

    except Exception as e:
        logger.error(f"分类过程中发生错误: {e}")
        # 发生错误时的默认分类逻辑
        return _get_default_classification(rule_data)


def _get_default_classification(rule_data: Dict[str, Any]) -> str:
    """
    获取默认分类结果，优先使用标准化分类逻辑（支持三元分类）

    Args:
        rule_data (Dict[str, Any]): 规则数据

    Returns:
        str: 默认分类结果（"规则"、"内涵"或"规则和内涵"）
    """
    # 首先尝试使用标准化分类逻辑
    try:
        standard_result = classify_rule_by_standards(rule_data)
        if standard_result in ["规则", "内涵", "规则和内涵"]:
            logger.info(f"使用标准化默认分类: {standard_result}")
            return standard_result
    except Exception as e:
        logger.warning(f"标准化默认分类失败: {e}")

    # 回退到原有的简单分类逻辑
    classification_chinese = rule_data.get('classification_chinese', '')
    category = rule_data.get('category', '')  # 兼容旧格式
    rule_type_chinese = rule_data.get('rule_type_chinese', '')

    # 基于规则类型的分类
    if rule_type_chinese == "时效性":
        return "规则"
    elif rule_type_chinese in ["段落完整性", "签名合理性"]:
        return "规则"
    elif rule_type_chinese in ["内容完整性", "数据一致性", "术语规范性"]:
        return "内涵"

    # 基于分类字段的分类（兼容旧逻辑）
    if classification_chinese in ['缺项', '时效']:
        return "规则"
    elif category in ['缺项', '时效']:
        return "规则"
    else:
        return "内涵"

def get_available_model_configs() -> Dict[str, Any]:
    """
    获取可用的模型配置

    Returns:
        Dict[str, Any]: 可用的模型配置字典
    """
    configs = {}

    try:
        from config import (
            qwen_30B_config, qwen_32B_config, qwen_72B_config
        )

        configs = {
            "qwen_30B_config": qwen_30B_config,
            "qwen_32B_config": qwen_32B_config,
            "qwen_72B_config": qwen_72B_config
        }

        logger.info(f"成功加载 {len(configs)} 个模型配置")

    except ImportError as e:
        logger.error(f"导入模型配置失败: {e}")

    return configs


if __name__ == '__main__':
    # 测试用例 - 新格式
    test_rule_new = {
        "rule_type_chinese": "段落完整性",
        "rule_type_english": "Section Completeness",
        "classification_chinese": "缺项",
        "classification_english": "Missing Item",
        "belonging_project_chinese": "病历首页诊疗信息",
        "belonging_project_english": "Medical Record Front Page Clinical Information",
        "document_type_chinese": "签名",
        "document_type_english": "Signature",
        "rule_content": "科主任签名(三级医院，可由病区负责医师代签)",
        "deduction_points": 0.5
    }

    # 测试用例 - 旧格式（兼容性测试）
    test_rule_old = {
        "rule_name": "首次病程缺诊断及诊断依据",
        "category": "缺项",
        "rule_type": "段落完整性",
        "description": "",
        "document_type": "Initial Progress Note"
    }

    print("=" * 60)
    print("规则类型分类器测试")
    print("=" * 60)

    # 测试新格式
    print("\n测试新格式规则:")
    result_new = classify_rule_type(test_rule_new)
    print(f"规则内容: {test_rule_new['rule_content']}")
    print(f"分类结果: {result_new}")

    # 测试旧格式
    print("\n测试旧格式规则（兼容性）:")
    result_old = classify_rule_type(test_rule_old)
    print(f"规则名称: {test_rule_old['rule_name']}")
    print(f"分类结果: {result_old}")

    # 显示可用模型配置
    print("\n可用的模型配置:")
    configs = get_available_model_configs()
    for i, (name, config) in enumerate(configs.items(), 1):
        model_name = config.get('model', 'Unknown')
        print(f"  {i}. {name}: {model_name}")

    print("\n测试完成！")