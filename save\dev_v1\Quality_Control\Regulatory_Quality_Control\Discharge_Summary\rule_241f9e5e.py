"""
rule_241f9e5e - Signature Compliance
Check if medical record lacks required physician signatures (resident, attending or higher)
"""

def check_rule(medical_record):
    """
    Verify presence of valid physician signatures in medical record
    Returns True if any required signature is missing
    """
    try:
        # Define required physician titles
        required_titles = {
            '住院医师', '住院医生',
            '主治医师', '主治医生',
            '副主任医师', '副主任医生',
            '主任医师', '主任医生'
        }
        
        # Check if signatures field exists and is a list
        if not isinstance(medical_record.get('signatures'), list):
            return True
            
        # Check if any signature matches required titles
        has_valid_signature = any(
            sig.get('title') in required_titles
            for sig in medical_record['signatures']
            if isinstance(sig, dict) and 'title' in sig
        )
            
        # Return True if no valid signature found
        return not has_valid_signature
        
    except Exception as e:
        # Return True in case of any error during processing
        return True

# Test cases
if __name__ == "__main__":
    # Test case 1: No signatures
    assert check_rule({'signatures': []}) == True
    
    # Test case 2: Valid resident signature
    assert check_rule({
        'signatures': [{'title': '住院医师', 'name': '张三'}]
    }) == False
    
    # Test case 3: Val<PERSON> attending signature
    assert check_rule({
        'signatures': [{'title': '主治医师', 'name': '李四'}]
    }) == False
    
    # Test case 4: Only nurse signature
    assert check_rule({
        'signatures': [{'title': '护士', 'name': '王五'}]
    }) == True
    
    # Test case 5: Multiple signatures with valid one
    assert check_rule({
        'signatures': [
            {'title': '护士', 'name': '王五'},
            {'title': '副主任医师', 'name': '赵六'}
        ]
    }) == False
    
    # Test case 6: Malformed signatures field
    assert check_rule({'signatures': 'not a list'}) == True
    assert check_rule({}) == True
    
    print("All test cases passed!")