# 医疗记录列表生成器

## 目录结构

```
dev_v1/Medical_Record_List/
├── medical_record_list_generator.py    # 主要实现文件
├── category_translations_*.xlsx        # 生成的Excel统计表
├── category_translations_*.md          # 生成的Markdown统计表
├── medical_record_generator.log        # 日志文件
├── README.md                          # 说明文档
└── tests/                             # 测试和演示文件目录
    ├── test_basic.py                  # 基础功能测试
    ├── test_generator.py              # 生成器测试
    ├── test_optimized.py              # 优化功能测试
    ├── test_translation.py            # 翻译功能测试
    └── demo_complete.py               # 完整功能演示
```

## 核心功能

### medical_record_list_generator.py
主要实现文件，包含以下功能：

1. **Excel文件读取**：读取质控评分表Excel文件
2. **数据提取**：提取6个核心列的医疗文档数据
   - 特别处理：使用Excel中的"分类.1"列作为"分类"字段数据源
3. **类别统计**：统计4个特定类别的唯一值
4. **英文翻译**：使用LLM生成专业的英文翻译
5. **文件生成**：
   - JSON文件：按文书类型分类保存到 `Medical_Record_List_Json` 目录
   - Excel统计表：包含汇总信息和各类别工作表，保存到当前目录
   - Markdown统计表：包含翻译对照表，保存到当前目录

### 特性
- ✅ 优化的翻译提示词，确保专业性和一致性
- ✅ 标题大小写格式化（Title Case）
- ✅ 智能路径检测，支持多目录运行
- ✅ 超时控制机制，防止程序卡死
- ✅ 文件保存到当前目录
- ✅ 完整的错误处理和日志记录
- ✅ 支持多种LLM模型配置：
  - GLM-4.5-Flash (推荐)
  - DeepSeek R1/V3
  - Kimi K2
  - Qwen 30B/32B

## 使用方法

### 直接运行主程序
```bash
python medical_record_list_generator.py
```

### 运行测试
```bash
# 基础功能测试（不进行翻译）
cd tests
python test_basic.py

# 完整功能演示
python demo_complete.py

# 优化功能测试
python test_optimized.py
```

## 输出文件

### JSON文件 (保存到 `Medical_Record_List_Json/` 目录)
- 按文书类型分类的详细记录
- 包含中英文字段和元数据信息
- 文件命名：使用英文文档类型名称，如 `Admission_Record.json`

### Excel统计表 (保存到当前目录)
- 文件名：`category_translations_*.xlsx`
- 汇总信息工作表：包含总体统计和类别概览
- 各类别工作表：包含中英文对照表，按中文排序

### Markdown统计表 (保存到当前目录)
- 文件名：`category_translations_*.md`
- 统计摘要：总记录数、类别数量等
- 翻译对照表：各类别的中英文对照表格

## 依赖要求

- pandas：Excel文件读取
- openpyxl：Excel文件生成
- 自定义模块：model_use, config

## 技术说明

### Excel列映射处理
由于Excel文件"附件1. 质控评分表v2.0.xlsx"包含重复的"分类"列：
- **分类**列：包含值 `缺项`, `时效`, `内涵`
- **分类.1**列：包含值 `手术科室`, `非手术科室`, `病历首页`

程序已配置使用**分类.1**列作为"分类"字段的数据源，确保生成的JSON文件包含正确的分类信息。

### 数据提取映射
```
Excel列名 -> 程序字段名
规则类型 -> 规则类型
分类.1   -> 分类 (特殊映射)
所属项目 -> 所属项目
文书类型 -> 文书类型
规则内容 -> 规则内容
扣分     -> 扣分
```

## 注意事项

1. 确保Excel文件路径正确
2. 配置正确的LLM模型参数
3. 生成的文件包含时间戳，避免覆盖
4. 日志文件记录详细的执行过程
5. "分类"字段数据来自Excel的"分类.1"列
